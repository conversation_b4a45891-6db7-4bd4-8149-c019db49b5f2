<UserControl x:Class="DriverManagementSystem.Views.StatisticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled"
                  Padding="20">
        <StackPanel>
            
            <!-- Page Header -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,30">
                <TextBlock Text="📊"
                         FontSize="32"
                         Foreground="{StaticResource PrimaryBrush}"
                         VerticalAlignment="Center"
                         Margin="0,0,15,0"/>
                <TextBlock Text="تقرير المهمة"
                         FontSize="28"
                         FontWeight="Bold"
                         Foreground="{StaticResource TextPrimaryBrush}"
                         VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Reports Selection -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Visit Report Card -->
                <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,20,0"
                        Background="{StaticResource LightGrayBrush}"
                        BorderBrush="{StaticResource PrimaryBrush}"
                        BorderThickness="2">
                    <Button Style="{StaticResource TransparentButtonStyle}"
                            Command="{Binding NavigateToReportCommand}"
                            Padding="30">
                        <StackPanel>
                            <TextBlock Text="📄" FontSize="64"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="محضر الزيارة"
                                     FontSize="24"
                                     FontWeight="Bold"
                                     Foreground="{StaticResource TextPrimaryBrush}"
                                     HorizontalAlignment="Center"
                                     Margin="0,15,0,10"/>
                            <TextBlock Text="إنشاء محضر استخراج عروض الأسعار للزيارات الميدانية"
                                     FontSize="14"
                                     Foreground="{StaticResource TextSecondaryBrush}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Border>

                <!-- Driver Contract Card -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="20,0,0,0"
                        Background="{StaticResource LightGrayBrush}"
                        BorderBrush="{StaticResource AccentBrush}"
                        BorderThickness="2">
                    <Button Style="{StaticResource TransparentButtonStyle}"
                            Command="{Binding NavigateToContractCommand}"
                            Padding="30">
                        <StackPanel>
                            <TextBlock Text="📋" FontSize="64"
                                     Foreground="{StaticResource AccentBrush}"
                                     HorizontalAlignment="Center"/>
                            <TextBlock Text="عقد السائق"
                                     FontSize="24"
                                     FontWeight="Bold"
                                     Foreground="{StaticResource TextPrimaryBrush}"
                                     HorizontalAlignment="Center"
                                     Margin="0,15,0,10"/>
                            <TextBlock Text="إنشاء وطباعة عقود السائقين للزيارات الميدانية"
                                     FontSize="14"
                                     Foreground="{StaticResource TextSecondaryBrush}"
                                     HorizontalAlignment="Center"
                                     TextWrapping="Wrap"
                                     TextAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </Border>
            </Grid>

            <!-- Additional Info Section -->
            <Border Style="{StaticResource CardStyle}" Margin="0,0,0,30">
                <StackPanel Margin="30">
                    <TextBlock Text="📋 معلومات التقارير"
                             FontSize="20"
                             FontWeight="Bold"
                             Foreground="{StaticResource TextPrimaryBrush}"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,20"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="📄 محضر الزيارة:" FontWeight="Bold" FontSize="16" Margin="0,5"/>
                            <TextBlock Text="• إنشاء محضر استخراج عروض الأسعار" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• عرض بيانات الزيارة والمشاريع" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• قائمة السائقين وعروض الأسعار" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• طباعة وتصدير التقرير" FontSize="14" Margin="0,2"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="20,0,0,0">
                            <TextBlock Text="📋 عقد السائق:" FontWeight="Bold" FontSize="16" Margin="0,5"/>
                            <TextBlock Text="• إنشاء عقد رسمي مع السائق" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• تفاصيل الزيارة والمهمة" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• الشروط والأحكام" FontSize="14" Margin="0,2"/>
                            <TextBlock Text="• التوقيعات الرسمية" FontSize="14" Margin="0,2"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
