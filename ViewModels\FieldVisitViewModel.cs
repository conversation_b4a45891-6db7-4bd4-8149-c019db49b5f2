using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    public class FieldVisitViewModel : BindableBase
    {
        private readonly IDataService _dataService;
        private FieldVisit? _selectedFieldVisit;
        private string _visitNumber = string.Empty;
        private DateTime _addDate = DateTime.Now;
        private string _hijriDate = string.Empty;
        private DateTime _departureDate = DateTime.Now;
        private DateTime _returnDate = DateTime.Now.AddDays(1);
        private int _daysCount = 1;
        private string _missionPurpose = string.Empty;
        private Sector? _selectedSector;
        private int _visitorsCount = 1;
        private int _itineraryItemsCount = 1;
        private string _statusMessage = "النظام جاهز للاستخدام";
        private string _currentDate = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
        private string _currentTime = DateTime.Now.ToString("HH:mm:ss");

        public FieldVisitViewModel()
        {
            _dataService = new DataService();

            // Initialize collections
            FieldVisits = new ObservableCollection<FieldVisit>();
            Sectors = new ObservableCollection<Sector>();
            SectorOfficers = new ObservableCollection<Officer>();
            VisitorInputs = new ObservableCollection<VisitorInput>();
            ItineraryInputs = new ObservableCollection<ItineraryInput>();

            // Initialize commands
            AddCommand = new DelegateCommand(AddFieldVisit, CanExecuteAdd);
            SaveCommand = new DelegateCommand(SaveFieldVisit, CanExecuteSave);
            DeleteCommand = new DelegateCommand(DeleteFieldVisit, CanExecuteDelete);
            CalculateDaysCommand = new DelegateCommand(CalculateDays);
            IncreaseItineraryItemsCommand = new DelegateCommand(IncreaseItineraryItems);
            DecreaseItineraryItemsCommand = new DelegateCommand(DecreaseItineraryItems);

            // Load initial data
            LoadData();

            // Initialize inputs
            UpdateVisitorInputs();
            UpdateItineraryInputs();

            // Generate initial visit number
            GenerateVisitNumber();

            // Set initial hijri date
            UpdateHijriDate();

            // Calculate initial days
            CalculateDays();

            // Initialize visitor inputs
            UpdateVisitorInputs();

            // Initialize itinerary inputs
            UpdateItineraryInputs();

            // Start time update timer
            StartTimeUpdateTimer();

            // Update status
            StatusMessage = "النظام جاهز - يمكنك إضافة زيارة جديدة";
        }

        #region Properties

        public ObservableCollection<FieldVisit> FieldVisits { get; }
        public ObservableCollection<Sector> Sectors { get; }
        public ObservableCollection<Officer> SectorOfficers { get; }
        public ObservableCollection<VisitorInput> VisitorInputs { get; }
        public ObservableCollection<ItineraryInput> ItineraryInputs { get; }

        public FieldVisit? SelectedFieldVisit
        {
            get => _selectedFieldVisit;
            set
            {
                SetProperty(ref _selectedFieldVisit, value);
                LoadFieldVisitDetails();
                RaiseCanExecuteChanged();
            }
        }

        public string VisitNumber
        {
            get => _visitNumber;
            set => SetProperty(ref _visitNumber, value);
        }

        public DateTime AddDate
        {
            get => _addDate;
            set
            {
                SetProperty(ref _addDate, value);
                UpdateHijriDate();
            }
        }

        public string HijriDate
        {
            get => _hijriDate;
            set => SetProperty(ref _hijriDate, value);
        }

        public DateTime DepartureDate
        {
            get => _departureDate;
            set
            {
                if (SetProperty(ref _departureDate, value))
                {
                    CalculateDays();
                    System.Diagnostics.Debug.WriteLine($"📅 تم تغيير تاريخ النزول إلى: {value:dd/MM/yyyy}");
                }
            }
        }

        public DateTime ReturnDate
        {
            get => _returnDate;
            set
            {
                if (SetProperty(ref _returnDate, value))
                {
                    CalculateDays();
                    System.Diagnostics.Debug.WriteLine($"🏠 تم تغيير تاريخ العودة إلى: {value:dd/MM/yyyy}");
                }
            }
        }

        public int DaysCount
        {
            get => _daysCount;
            set => SetProperty(ref _daysCount, value);
        }

        public string MissionPurpose
        {
            get => _missionPurpose;
            set => SetProperty(ref _missionPurpose, value);
        }

        public Sector? SelectedSector
        {
            get => _selectedSector;
            set
            {
                SetProperty(ref _selectedSector, value);
                LoadSectorOfficers();
                RaiseCanExecuteChanged();
            }
        }

        public int VisitorsCount
        {
            get => _visitorsCount;
            set
            {
                SetProperty(ref _visitorsCount, value);
                UpdateVisitorInputs();
            }
        }

        public int ItineraryItemsCount
        {
            get => _itineraryItemsCount;
            set
            {
                System.Diagnostics.Debug.WriteLine($"تحديث عدد عناصر خط السير من {_itineraryItemsCount} إلى {value}");
                SetProperty(ref _itineraryItemsCount, value);
                UpdateItineraryInputs();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string CurrentDate
        {
            get => _currentDate;
            set => SetProperty(ref _currentDate, value);
        }

        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public int TotalParticipants
        {
            get => FieldVisits.Sum(v => v.VisitorsCount);
        }

        public int ActiveSectorsCount
        {
            get => FieldVisits.Select(v => v.SectorId).Distinct().Count();
        }

        #endregion

        #region Commands

        public DelegateCommand AddCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand DeleteCommand { get; }
        public DelegateCommand CalculateDaysCommand { get; }
        public DelegateCommand IncreaseItineraryItemsCommand { get; }
        public DelegateCommand DecreaseItineraryItemsCommand { get; }

        #endregion

        #region Command Methods

        private async void AddFieldVisit()
        {
            try
            {
                var newVisit = new FieldVisit
                {
                    VisitNumber = VisitNumber,
                    AddDate = AddDate,
                    HijriDate = HijriDate,
                    DepartureDate = DepartureDate,
                    ReturnDate = ReturnDate,
                    DaysCount = DaysCount,
                    MissionPurpose = MissionPurpose,
                    SectorId = SelectedSector?.Id ?? 0,
                    SectorName = SelectedSector?.Name ?? string.Empty,
                    VisitorsCount = VisitorsCount,
                    Visitors = VisitorInputs.Where(v => !string.IsNullOrWhiteSpace(v.Name))
                                           .Select(v => new FieldVisitor
                                           {
                                               Name = v.Name,
                                               OfficerId = v.SelectedOfficer?.Id ?? 0,
                                               OfficerName = v.SelectedOfficer?.Name ?? string.Empty,
                                               OfficerRank = v.SelectedOfficer?.Rank ?? string.Empty,
                                               OfficerCode = v.SelectedOfficer?.Code ?? string.Empty
                                           }).ToList(),
                    // إضافة خط السير من الواجهة
                    Itinerary = new System.Collections.Generic.List<string>()
                };

                // جمع خط السير من الواجهة
                System.Diagnostics.Debug.WriteLine($"عدد أيام خط السير: {ItineraryInputs.Count}");
                foreach (var itineraryInput in ItineraryInputs)
                {
                    var itineraryText = string.IsNullOrWhiteSpace(itineraryInput.ItineraryText)
                        ? $"اليوم {itineraryInput.DayNumber}: لم يتم تحديد خط السير"
                        : itineraryInput.ItineraryText;
                    newVisit.Itinerary.Add(itineraryText);
                    System.Diagnostics.Debug.WriteLine($"إضافة خط السير لليوم {itineraryInput.DayNumber}: {itineraryText}");
                }

                // حفظ في قاعدة البيانات
                System.Diagnostics.Debug.WriteLine($"🔄 محاولة حفظ الزيارة: {newVisit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"📊 عدد عناصر خط السير: {newVisit.Itinerary.Count}");
                foreach (var item in newVisit.Itinerary)
                {
                    System.Diagnostics.Debug.WriteLine($"📝 عنصر خط السير: {item}");
                }

                var (success, errors) = await _dataService.AddFieldVisitAsync(newVisit);
                System.Diagnostics.Debug.WriteLine($"✅ نتيجة الحفظ: {success}");

                if (success)
                {
                    // حفظ رقم الزيارة قبل التفريغ
                    var savedVisitNumber = newVisit.VisitNumber;
                    var savedSectorName = newVisit.SectorName;
                    var savedVisitorsCount = newVisit.VisitorsCount;

                    // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التحديث
                    await RefreshFieldVisitsAsync();
                    UpdateStatistics();

                    // عرض رسالة النجاح أولاً
                    MessageBox.Show($"✅ تم إضافة الزيارة الميدانية بنجاح\n\n🔢 رقم الزيارة: {savedVisitNumber}\n🏢 القطاع: {savedSectorName}\n👥 عدد المشاركين: {savedVisitorsCount}\n\n🆕 تم حفظ جميع البيانات بنجاح في النظام", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                    // تفريغ النموذج بعد إغلاق الرسالة
                    ClearForm();
                    GenerateVisitNumber();

                    // تحديث رسالة الحالة
                    StatusMessage = $"تم إضافة الزيارة {savedVisitNumber} بنجاح - النموذج جاهز لزيارة جديدة";
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ فشل في حفظ الزيارة في قاعدة البيانات");
                    var errorMessage = "❌ فشل في إضافة الزيارة الميدانية:\n\n";
                    for (int i = 0; i < errors.Count; i++)
                    {
                        errorMessage += $"{i + 1}. {errors[i]}\n";
                    }
                    MessageBox.Show(errorMessage, "أخطاء في البيانات", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الزيارة الميدانية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveFieldVisit()
        {
            try
            {
                if (SelectedFieldVisit == null) return;

                SelectedFieldVisit.AddDate = AddDate;
                SelectedFieldVisit.HijriDate = HijriDate;
                SelectedFieldVisit.DepartureDate = DepartureDate;
                SelectedFieldVisit.ReturnDate = ReturnDate;
                SelectedFieldVisit.DaysCount = DaysCount;
                SelectedFieldVisit.MissionPurpose = MissionPurpose;
                SelectedFieldVisit.SectorId = SelectedSector?.Id ?? 0;
                SelectedFieldVisit.SectorName = SelectedSector?.Name ?? string.Empty;
                SelectedFieldVisit.VisitorsCount = VisitorsCount;
                SelectedFieldVisit.Visitors = VisitorInputs.Where(v => !string.IsNullOrWhiteSpace(v.Name))
                                                          .Select(v => new FieldVisitor
                                                          {
                                                              Name = v.Name,
                                                              OfficerId = v.SelectedOfficer?.Id ?? 0,
                                                              OfficerName = v.SelectedOfficer?.Name ?? string.Empty,
                                                              OfficerRank = v.SelectedOfficer?.Rank ?? string.Empty,
                                                              OfficerCode = v.SelectedOfficer?.Code ?? string.Empty
                                                          }).ToList();

                // تحديث خط السير
                SelectedFieldVisit.Itinerary.Clear();
                foreach (var itineraryInput in ItineraryInputs)
                {
                    var itineraryText = string.IsNullOrWhiteSpace(itineraryInput.ItineraryText)
                        ? $"اليوم {itineraryInput.DayNumber}: لم يتم تحديد خط السير"
                        : itineraryInput.ItineraryText;
                    SelectedFieldVisit.Itinerary.Add(itineraryText);
                }

                // تحديث في قاعدة البيانات
                var success = await _dataService.UpdateFieldVisitAsync(SelectedFieldVisit);

                if (success)
                {
                    // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التحديث
                    await RefreshFieldVisitsAsync();
                    UpdateStatistics();
                    StatusMessage = $"تم تحديث الزيارة {SelectedFieldVisit.VisitNumber} بنجاح";
                    MessageBox.Show("✅ تم حفظ التعديلات بنجاح", "نجح العملية", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteFieldVisit()
        {
            try
            {
                if (SelectedFieldVisit == null) return;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الزيارة رقم: {SelectedFieldVisit.VisitNumber}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف من قاعدة البيانات
                    var success = await _dataService.DeleteFieldVisitAsync(SelectedFieldVisit.Id);

                    if (success)
                    {
                        var deletedVisitNumber = SelectedFieldVisit.VisitNumber;
                        // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التحديث
                        await RefreshFieldVisitsAsync();
                        ClearForm();
                        GenerateVisitNumber();
                        UpdateStatistics();
                        StatusMessage = $"تم حذف الزيارة {deletedVisitNumber} بنجاح";
                        MessageBox.Show($"✅ تم حذف الزيارة الميدانية بنجاح\n\n🔢 رقم الزيارة المحذوفة: {deletedVisitNumber}", "نجح العملية", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الزيارة الميدانية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الزيارة الميدانية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Can Execute Methods

        private bool CanExecuteAdd()
        {
            return SelectedSector != null && !string.IsNullOrWhiteSpace(MissionPurpose) && VisitorsCount > 0;
        }

        private bool CanExecuteSave()
        {
            return SelectedFieldVisit != null && SelectedSector != null && !string.IsNullOrWhiteSpace(MissionPurpose);
        }

        private bool CanExecuteDelete()
        {
            return SelectedFieldVisit != null;
        }

        #endregion

        #region Helper Methods

        private async void LoadData()
        {
            try
            {
                // Load sectors
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }

                // Load field visits
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits)
                {
                    FieldVisits.Add(visit);
                }

                UpdateStatistics();
                StatusMessage = $"تم تحميل {FieldVisits.Count} زيارة ميدانية";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadSectorOfficers()
        {
            try
            {
                if (SelectedSector == null) return;

                var officers = await _dataService.GetOfficersBySectorAsync(SelectedSector.Id);
                SectorOfficers.Clear();
                foreach (var officer in officers)
                {
                    SectorOfficers.Add(officer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل موظفي القطاع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateHijriDate()
        {
            HijriDate = HijriDateService.ConvertToHijri(AddDate);
        }

        private void CalculateDays()
        {
            var newDaysCount = HijriDateService.CalculateDaysBetween(DepartureDate, ReturnDate);
            DaysCount = newDaysCount;
            System.Diagnostics.Debug.WriteLine($"🔢 تم تحديث عدد الأيام: {DaysCount} (من {DepartureDate:dd/MM/yyyy} إلى {ReturnDate:dd/MM/yyyy})");
            // خط السير مستقل تماماً عن عدد الأيام
        }

        private void IncreaseItineraryItems()
        {
            if (ItineraryItemsCount < 20) // حد أقصى 20 عنصر
            {
                ItineraryItemsCount++;
                System.Diagnostics.Debug.WriteLine($"➕ زيادة عناصر خط السير إلى: {ItineraryItemsCount}");
            }
        }

        private void DecreaseItineraryItems()
        {
            if (ItineraryItemsCount > 1) // حد أدنى عنصر واحد
            {
                ItineraryItemsCount--;
                System.Diagnostics.Debug.WriteLine($"➖ تقليل عناصر خط السير إلى: {ItineraryItemsCount}");
            }
        }

        private async void GenerateVisitNumber()
        {
            try
            {
                var visits = await _dataService.GetFieldVisitsAsync();
                var maxNumber = 0;

                foreach (var visit in visits)
                {
                    if (!string.IsNullOrEmpty(visit.VisitNumber) && visit.VisitNumber.StartsWith("V"))
                    {
                        var numberPart = visit.VisitNumber.Substring(1);
                        if (int.TryParse(numberPart, out int number))
                        {
                            maxNumber = Math.Max(maxNumber, number);
                        }
                    }
                }

                VisitNumber = $"V{(maxNumber + 1):D4}"; // مثل V0001, V0002
            }
            catch (Exception)
            {
                VisitNumber = $"V{DateTime.Now:HHmmss}";
            }
        }

        private void UpdateVisitorInputs()
        {
            VisitorInputs.Clear();
            for (int i = 1; i <= VisitorsCount; i++)
            {
                VisitorInputs.Add(new VisitorInput { Number = $"{i}." });
            }
        }

        private void UpdateItineraryInputs()
        {
            ItineraryInputs.Clear();
            for (int i = 1; i <= ItineraryItemsCount; i++)
            {
                ItineraryInputs.Add(new ItineraryInput { DayNumber = i, DayLabel = $"العنصر {i}" });
            }
            System.Diagnostics.Debug.WriteLine($"🗺️ تم تحديث خط السير - العدد: {ItineraryItemsCount}");
        }

        private void LoadFieldVisitDetails()
        {
            if (SelectedFieldVisit == null)
            {
                ClearForm();
                return;
            }

            AddDate = SelectedFieldVisit.AddDate;
            HijriDate = SelectedFieldVisit.HijriDate;
            DepartureDate = SelectedFieldVisit.DepartureDate;
            ReturnDate = SelectedFieldVisit.ReturnDate;
            DaysCount = SelectedFieldVisit.DaysCount;
            MissionPurpose = SelectedFieldVisit.MissionPurpose;
            SelectedSector = Sectors.FirstOrDefault(s => s.Id == SelectedFieldVisit.SectorId);
            VisitorsCount = SelectedFieldVisit.VisitorsCount;
            // خط السير مستقل - نحتفظ بالعدد الحالي أو نضع العدد المحفوظ
            ItineraryItemsCount = SelectedFieldVisit.Itinerary.Count > 0 ? SelectedFieldVisit.Itinerary.Count : ItineraryItemsCount;

            // Load visitors
            UpdateVisitorInputs();
            for (int i = 0; i < SelectedFieldVisit.Visitors.Count && i < VisitorInputs.Count; i++)
            {
                VisitorInputs[i].Name = SelectedFieldVisit.Visitors[i].Name;
                VisitorInputs[i].SelectedOfficer = SectorOfficers.FirstOrDefault(o => o.Id == SelectedFieldVisit.Visitors[i].OfficerId);
            }

            // Load itinerary
            UpdateItineraryInputs();
            for (int i = 0; i < SelectedFieldVisit.Itinerary.Count && i < ItineraryInputs.Count; i++)
            {
                ItineraryInputs[i].ItineraryText = SelectedFieldVisit.Itinerary[i];
            }
        }

        private void ClearForm()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 بدء تفريغ النموذج...");

                // تفريغ التواريخ
                AddDate = DateTime.Now;
                UpdateHijriDate();
                DepartureDate = DateTime.Now;
                ReturnDate = DateTime.Now.AddDays(1);
                CalculateDays();

                // تفريغ البيانات الأساسية
                MissionPurpose = string.Empty;
                SelectedSector = null;
                SelectedFieldVisit = null;

                // إعادة تعيين العدادات
                VisitorsCount = 1;
                ItineraryItemsCount = 1;

                // تحديث قوائم الإدخال وتفريغها
                UpdateVisitorInputs();
                UpdateItineraryInputs();

                // تفريغ محتوى الحقول النصية في القوائم
                foreach (var visitor in VisitorInputs)
                {
                    visitor.Name = string.Empty;
                    visitor.SelectedOfficer = null;
                }

                foreach (var itinerary in ItineraryInputs)
                {
                    itinerary.ItineraryText = string.Empty;
                }

                // تحديث حالة الأزرار
                RaiseCanExecuteChanged();

                // تحديث جميع الخصائص
                RaisePropertyChanged(string.Empty);

                System.Diagnostics.Debug.WriteLine("✅ تم تفريغ النموذج بالكامل بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفريغ النموذج: {ex.Message}");
            }
        }

        private void RaiseCanExecuteChanged()
        {
            AddCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
        }

        private void StartTimeUpdateTimer()
        {
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += (s, e) =>
            {
                CurrentTime = DateTime.Now.ToString("HH:mm:ss");
                CurrentDate = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
            };
            timer.Start();
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalParticipants));
            RaisePropertyChanged(nameof(ActiveSectorsCount));
        }

        private async Task RefreshFieldVisitsAsync()
        {
            try
            {
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();
                foreach (var visit in visits.OrderByDescending(v => v.AddDate))
                {
                    FieldVisits.Add(visit);
                }
                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث قائمة الزيارات - العدد: {FieldVisits.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث قائمة الزيارات: {ex.Message}");
            }
        }

        #endregion
    }

    public class VisitorInput : BindableBase
    {
        private string _number = string.Empty;
        private string _name = string.Empty;
        private Officer? _selectedOfficer;

        public string Number
        {
            get => _number;
            set => SetProperty(ref _number, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public Officer? SelectedOfficer
        {
            get => _selectedOfficer;
            set => SetProperty(ref _selectedOfficer, value);
        }
    }

    public class ItineraryInput : BindableBase
    {
        private int _dayNumber;
        private string _dayLabel = string.Empty;
        private string _itineraryText = string.Empty;

        public int DayNumber
        {
            get => _dayNumber;
            set => SetProperty(ref _dayNumber, value);
        }

        public string DayLabel
        {
            get => _dayLabel;
            set => SetProperty(ref _dayLabel, value);
        }

        public string ItineraryText
        {
            get => _itineraryText;
            set => SetProperty(ref _itineraryText, value);
        }
    }
}