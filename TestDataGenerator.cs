using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem
{
    /// <summary>
    /// مولد البيانات التجريبية لاختبار النظام
    /// </summary>
    public class TestDataGenerator
    {
        private readonly ApplicationDbContext _context;

        public TestDataGenerator()
        {
            _context = new ApplicationDbContext();
        }

        /// <summary>
        /// إنشاء بيانات تجريبية شاملة
        /// </summary>
        public async Task GenerateAllTestDataAsync()
        {
            try
            {
                Console.WriteLine("🚀 بدء إنشاء البيانات التجريبية...");

                await GenerateTestDriversAsync();
                await GenerateTestFieldVisitsAsync();
                await GenerateTestDriverQuotesAsync();

                Console.WriteLine("✅ تم إنشاء جميع البيانات التجريبية بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء البيانات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء سائقين تجريبيين
        /// </summary>
        public async Task GenerateTestDriversAsync()
        {
            try
            {
                var existingDrivers = await _context.Drivers.CountAsync();
                if (existingDrivers >= 5)
                {
                    Console.WriteLine("✅ يوجد سائقين كافيين في النظام");
                    return;
                }

                var testDrivers = new List<Driver>
                {
                    new Driver
                    {
                        Name = "أحمد محمد العريضي",
                        DriverCode = "D001",
                        PhoneNumber = "777123456",
                        VehicleType = "تويوتا هايلكس",
                        VehicleNumber = "ذ أ ب 1234",
                        SectorName = "ذمار",
                        IsActive = true
                    },
                    new Driver
                    {
                        Name = "علي أحمد السودائي",
                        DriverCode = "D002",
                        PhoneNumber = "777654321",
                        VehicleType = "نيسان باترول",
                        VehicleNumber = "ذ ج د 5678",
                        SectorName = "ذمار",
                        IsActive = true
                    },
                    new Driver
                    {
                        Name = "حميد عبدالله السهوري",
                        DriverCode = "D003",
                        PhoneNumber = "777987654",
                        VehicleType = "فورد رينجر",
                        VehicleNumber = "ذ هـ و 9012",
                        SectorName = "ذمار",
                        IsActive = true
                    },
                    new Driver
                    {
                        Name = "بشير ابراهيم المحجري",
                        DriverCode = "D004",
                        PhoneNumber = "777456789",
                        VehicleType = "شيفروليه كولورادو",
                        VehicleNumber = "ذ ز ح 3456",
                        SectorName = "ذمار",
                        IsActive = true
                    },
                    new Driver
                    {
                        Name = "محمد صالح الشامي",
                        DriverCode = "D005",
                        PhoneNumber = "777321654",
                        VehicleType = "إيسوزو ديماكس",
                        VehicleNumber = "ذ ط ي 7890",
                        SectorName = "ذمار",
                        IsActive = true
                    }
                };

                foreach (var driver in testDrivers)
                {
                    var existing = await _context.Drivers.FirstOrDefaultAsync(d => d.DriverCode == driver.DriverCode);
                    if (existing == null)
                    {
                        _context.Drivers.Add(driver);
                    }
                }

                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم إنشاء {testDrivers.Count} سائق تجريبي");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء السائقين التجريبيين: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء زيارات ميدانية تجريبية
        /// </summary>
        public async Task GenerateTestFieldVisitsAsync()
        {
            try
            {
                var existingVisits = await _context.FieldVisits.CountAsync();
                if (existingVisits >= 3)
                {
                    Console.WriteLine("✅ يوجد زيارات ميدانية كافية في النظام");
                    return;
                }

                var testVisits = new List<FieldVisit>
                {
                    new FieldVisit
                    {
                        VisitNumber = "2025001",
                        DepartureDate = DateTime.Now.AddDays(1),
                        ReturnDate = DateTime.Now.AddDays(4),
                        DaysCount = 3,
                        SelectedDrivers = "أحمد محمد العريضي-50000|علي أحمد السودائي-45000|حميد عبدالله السهوري-55000",
                        DriverContract = "أحمد محمد العريضي",
                        WinnerDriverMessage = "تم اختياركم للزيارة الميدانية رقم 2025001"
                    },
                    new FieldVisit
                    {
                        VisitNumber = "2025002",
                        DepartureDate = DateTime.Now.AddDays(7),
                        ReturnDate = DateTime.Now.AddDays(10),
                        DaysCount = 3,
                        SelectedDrivers = "بشير ابراهيم المحجري-48000|محمد صالح الشامي-52000",
                        DriverContract = "بشير ابراهيم المحجري",
                        WinnerDriverMessage = "تم اختياركم للزيارة الميدانية رقم 2025002"
                    },
                    new FieldVisit
                    {
                        VisitNumber = "2025003",
                        DepartureDate = DateTime.Now.AddDays(14),
                        ReturnDate = DateTime.Now.AddDays(17),
                        DaysCount = 3,
                        SelectedDrivers = "علي أحمد السودائي-47000|حميد عبدالله السهوري-53000|أحمد محمد العريضي-49000",
                        DriverContract = "علي أحمد السودائي",
                        WinnerDriverMessage = "تم اختياركم للزيارة الميدانية رقم 2025003"
                    }
                };

                foreach (var visit in testVisits)
                {
                    var existing = await _context.FieldVisits.FirstOrDefaultAsync(v => v.VisitNumber == visit.VisitNumber);
                    if (existing == null)
                    {
                        _context.FieldVisits.Add(visit);
                    }
                }

                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم إنشاء {testVisits.Count} زيارة ميدانية تجريبية");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء الزيارات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء عروض أسعار تجريبية
        /// </summary>
        public async Task GenerateTestDriverQuotesAsync()
        {
            try
            {
                var existingQuotes = await _context.DriverQuotes.CountAsync();
                if (existingQuotes >= 5)
                {
                    Console.WriteLine("✅ يوجد عروض أسعار كافية في النظام");
                    return;
                }

                var drivers = await _context.Drivers.Take(5).ToListAsync();
                var visits = await _context.FieldVisits.Take(3).ToListAsync();

                if (!drivers.Any() || !visits.Any())
                {
                    Console.WriteLine("⚠️ لا توجد سائقين أو زيارات لإنشاء عروض الأسعار");
                    return;
                }

                var testQuotes = new List<DriverQuote>();
                var random = new Random();

                foreach (var visit in visits)
                {
                    var selectedDrivers = drivers.Take(3).ToList();
                    
                    for (int i = 0; i < selectedDrivers.Count; i++)
                    {
                        var driver = selectedDrivers[i];
                        var basePrice = 45000 + (i * 5000);
                        var finalPrice = basePrice + random.Next(-3000, 3000);

                        var quote = new DriverQuote
                        {
                            DriverId = driver.Id,
                            DriverName = driver.Name,
                            DriverCode = driver.DriverCode,
                            PhoneNumber = driver.PhoneNumber,
                            VehicleType = driver.VehicleType,
                            VehicleNumber = driver.VehicleNumber,
                            QuotedPrice = finalPrice,
                            QuotedDays = visit.DaysCount,
                            Notes = $"عرض سعر للزيارة {visit.VisitNumber}",
                            Status = i == 0 ? QuoteStatus.Accepted : QuoteStatus.Pending,
                            QuoteDate = DateTime.Now
                        };

                        testQuotes.Add(quote);
                    }
                }

                foreach (var quote in testQuotes)
                {
                    var existing = await _context.DriverQuotes.FirstOrDefaultAsync(q => 
                        q.DriverId == quote.DriverId && q.Notes == quote.Notes);
                    if (existing == null)
                    {
                        _context.DriverQuotes.Add(quote);
                    }
                }

                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ تم إنشاء {testQuotes.Count} عرض سعر تجريبي");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء عروض الأسعار التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف جميع البيانات التجريبية
        /// </summary>
        public async Task ClearAllTestDataAsync()
        {
            try
            {
                Console.WriteLine("🗑️ بدء حذف البيانات التجريبية...");

                // حذف عروض الأسعار
                var quotes = await _context.DriverQuotes.ToListAsync();
                _context.DriverQuotes.RemoveRange(quotes);

                // حذف الزيارات
                var visits = await _context.FieldVisits.ToListAsync();
                _context.FieldVisits.RemoveRange(visits);

                // حذف السائقين (اختياري)
                // var drivers = await _context.Drivers.ToListAsync();
                // _context.Drivers.RemoveRange(drivers);

                await _context.SaveChangesAsync();
                Console.WriteLine("✅ تم حذف البيانات التجريبية بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حذف البيانات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض إحصائيات البيانات الحالية
        /// </summary>
        public async Task ShowDataStatisticsAsync()
        {
            try
            {
                var driversCount = await _context.Drivers.CountAsync();
                var visitsCount = await _context.FieldVisits.CountAsync();
                var quotesCount = await _context.DriverQuotes.CountAsync();

                Console.WriteLine("📊 إحصائيات البيانات الحالية:");
                Console.WriteLine($"   👥 السائقين: {driversCount}");
                Console.WriteLine($"   🚗 الزيارات الميدانية: {visitsCount}");
                Console.WriteLine($"   💰 عروض الأسعار: {quotesCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في عرض الإحصائيات: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// مثال على الاستخدام
    /// </summary>
    public class TestDataUsageExample
    {
        public static async Task RunExample()
        {
            var generator = new TestDataGenerator();

            try
            {
                // عرض الإحصائيات الحالية
                await generator.ShowDataStatisticsAsync();

                // إنشاء البيانات التجريبية
                await generator.GenerateAllTestDataAsync();

                // عرض الإحصائيات بعد الإنشاء
                await generator.ShowDataStatisticsAsync();
            }
            finally
            {
                generator.Dispose();
            }
        }
    }
}
