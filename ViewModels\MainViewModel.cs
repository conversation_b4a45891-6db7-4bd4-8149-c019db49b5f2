using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.ViewModels
{
    public class MainViewModel : BindableBase
    {
        private readonly IAuthenticationService _authService;
        private readonly ErrorHandlingService _errorHandler;

        private object? _currentView;
        private string _selectedMenuItem = "Dashboard";
        private bool _isSidebarCollapsed = false;

        // Cache for views to avoid recreating them
        private readonly Dictionary<string, object> _viewCache = new Dictionary<string, object>();

        public MainViewModel(IAuthenticationService authService)
        {
            _authService = authService;
            _errorHandler = new ErrorHandlingService();

            // Initialize commands
            NavigateCommand = new DelegateCommand<string>(Navigate);
            LogoutCommand = new DelegateCommand(async () => await LogoutAsync());
            ToggleSidebarCommand = new DelegateCommand(ToggleSidebar);

            // Set initial view
            Navigate("Dashboard");
        }

        public User? CurrentUser => _authService.CurrentUser;

        public object? CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public string SelectedMenuItem
        {
            get => _selectedMenuItem;
            set => SetProperty(ref _selectedMenuItem, value);
        }

        public bool IsSidebarCollapsed
        {
            get => _isSidebarCollapsed;
            set => SetProperty(ref _isSidebarCollapsed, value);
        }



        public DelegateCommand<string> NavigateCommand { get; }
        public DelegateCommand LogoutCommand { get; }
        public DelegateCommand ToggleSidebarCommand { get; }


        public event EventHandler? LogoutRequested;

        public void Navigate(string viewName)
        {
            try
            {
                SelectedMenuItem = viewName;

                // Views that should always refresh (don't cache)
                var alwaysRefreshViews = new[] { "FieldVisitsLog", "Dashboard", "Statistics" };

                // Check if view should be refreshed or can use cache
                if (!alwaysRefreshViews.Contains(viewName) && _viewCache.ContainsKey(viewName))
                {
                    CurrentView = _viewCache[viewName];
                    return;
                }

                // Create new view with professional templates enabled
                object newView = viewName switch
                {
                    "Dashboard" => new Views.DashboardView { DataContext = new DashboardViewModel() },
                    "Statistics" => new Views.StatisticsView { DataContext = new StatisticsViewModel() },
                    "UserManagement" => new Views.SimpleUserManagementView(),
                    "DropData" => CreateDropDataView(),
                    "FieldVisitsLog" => new Views.FieldVisitsLogView(), // Always create fresh
                    "Search" => new Views.SearchView { DataContext = new SearchViewModel() },
                    "Route" => null, // تم حذف RouteView
                    "Contracts" => new Views.ContractsView { DataContext = new ContractsViewModel() },
                    _ => new Views.DashboardView { DataContext = new DashboardViewModel() }
                };

                // Cache the view only if it's not in the always refresh list
                if (!alwaysRefreshViews.Contains(viewName))
                {
                    _viewCache[viewName] = newView;
                }

                CurrentView = newView;

                System.Diagnostics.Debug.WriteLine($"🔄 تم إنشاء صفحة جديدة: {viewName}");
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الأخطاء المتقدم
                var errorResult = _errorHandler.HandleException(ex,
                    context: $"التنقل إلى صفحة {viewName}",
                    userAction: $"النقر على قائمة {GetPageDisplayName(viewName)}");

                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التنقل إلى {viewName}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
                System.Diagnostics.Debug.WriteLine($"🆔 رقم الخطأ: {errorResult.ErrorId}");

                // عرض رسالة خطأ تفصيلية للمستخدم
                System.Windows.MessageBox.Show(
                    errorResult.UserMessage,
                    "❌ خطأ في التنقل",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error
                );

                // Fallback to dashboard
                try
                {
                    if (!_viewCache.ContainsKey("Dashboard"))
                    {
                        _viewCache["Dashboard"] = new Views.DashboardView { DataContext = new DashboardViewModel() };
                    }
                    CurrentView = _viewCache["Dashboard"];
                    SelectedMenuItem = "Dashboard";
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في العودة للوحة التحكم: {fallbackEx.Message}");
                }
            }
        }





        private object CreateDropDataView()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Creating DropDataView...");
                var viewModel = new DropDataViewModel();
                System.Diagnostics.Debug.WriteLine("DropDataViewModel created successfully");

                var view = new Views.DropDataView { DataContext = viewModel };
                System.Diagnostics.Debug.WriteLine("DropDataView created successfully");

                return view;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating DropDataView: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Return a simple error view
                var errorView = new Views.DashboardView { DataContext = new DashboardViewModel() };
                System.Windows.MessageBox.Show($"خطأ في تحميل صفحة بيانات النزول:\n{ex.Message}", "خطأ", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                return errorView;
            }
        }

        private void ToggleSidebar()
        {
            IsSidebarCollapsed = !IsSidebarCollapsed;
        }

        private async System.Threading.Tasks.Task LogoutAsync()
        {
            await _authService.LogoutAsync();
            LogoutRequested?.Invoke(this, EventArgs.Empty);
        }

        private string GetPageDisplayName(string viewName)
        {
            return viewName switch
            {
                "Dashboard" => "لوحة التحكم",
                "Statistics" => "الإحصائيات",
                "UserManagement" => "إدارة المستخدمين",
                "DropData" => "بيانات النزول",
                "FieldVisitsLog" => "سجل الزيارات الميدانية",
                "Search" => "البحث",
                "Route" => "المسار",
                "Contracts" => "العقود",
                _ => viewName
            };
        }


    }

    // Placeholder ViewModels for navigation
    public class SearchViewModel : BindableBase { }
    public class ContractsViewModel : BindableBase { }
}
