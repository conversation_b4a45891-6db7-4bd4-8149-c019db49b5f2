<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- A4 Report Container - All Pages in One View -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header with Edit Contract Button -->
        <Border Grid.Row="0" Background="#F8F9FA" Padding="15,10" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="📄 تقرير الزيارة الميدانية والعقود"
                         FontSize="16" FontWeight="Bold" Foreground="#495057" VerticalAlignment="Center"/>

                <Button Grid.Column="1" Content="📝 تحرير قالب العقد"
                        Click="EditContractButton_Click"
                        Background="#007BFF"
                        Foreground="White"
                        Padding="15,8"
                        FontWeight="Bold"
                        BorderThickness="0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Cursor" Value="Hand"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#0056B3"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- Report Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" Background="White">
            <StackPanel Background="White" HorizontalAlignment="Center">

        <!-- الصفحة الأولى: محضر استخراج عروض الأسعار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="5">

                <!-- Compact Professional Header -->
                <Grid Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Side - Organization Header -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E3A8A" Margin="0,2,0,0"/>
                        <TextBlock Text="فرع ذمار والبيضاء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                    </StackPanel>

                    <!-- Center - Main Title -->
                    <Border Grid.Column="1" Background="#E8F4FD" BorderBrush="#4682B4" BorderThickness="2" Padding="20,10" HorizontalAlignment="Center">
                        <TextBlock Text="محضر استدراج عروض اسعار" FontSize="20" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#1E3A8A" TextAlignment="Center"/>
                    </Border>

                    <!-- Right Side - Date and Visit Number with Icons -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="8,4" Margin="0,0,0,4">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="التاريخ: "/>
                                    <Run Text="{Binding ReportData.ReportDate, FallbackValue='15/06/2025'}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="رقم الزيارة: "/>
                                    <Run Text="{Binding ReportData.VisitNumber, FallbackValue='911-13031'}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>

                    <!-- Compact Projects Section -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                        <StackPanel>
                            <!-- Compact Header with Sector -->
                            <Border Background="#F8F9FA" Padding="8,6">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Projects Title on the left -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                        <TextBlock Text="المشاريع التي سيتم زيارتها"
                                                 FontWeight="Bold" FontSize="12" HorizontalAlignment="Left" Foreground="Black"/>
                                    </StackPanel>

                                    <!-- Empty space in center -->
                                    <TextBlock Grid.Column="1"/>

                                    <!-- Sector on the right with Icon -->
                                    <Border Grid.Column="2" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                        <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black">
                                            <Run Text="🏢 القطاع: "/>
                                            <Run Text="{Binding ReportData.SectorName, FallbackValue='الصحة والحماية الاجتماعية'}"/>
                                        </TextBlock>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Header -->
                            <Border BorderBrush="Black" BorderThickness="2" Background="#E8F4FD">
                                <Grid MinHeight="35">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="80"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                        <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                        <TextBlock Text="اسم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="2" Padding="8,8">
                                        <TextBlock Text="عدد الأيام" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Data -->
                            <ItemsControl ItemsSource="{Binding ReportData.Projects}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                            <Grid MinHeight="35" Background="White">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                    <TextBlock Text="{Binding ProjectNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="SemiBold" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                    <TextBlock Text="{Binding ProjectName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" TextWrapping="Wrap"
                                                             FontSize="11" LineHeight="16" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="2" Padding="8,6">
                                                    <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                </Border>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- Compact Visit Data Section -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                        <StackPanel>
                            <!-- Compact Header -->
                            <Border Background="#4682B4" Padding="8,6">
                                <TextBlock Text="بيانات الزيارة الميدانية" FontWeight="Bold" FontSize="11"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>

                            <!-- Compact Content -->
                            <StackPanel Margin="10,8">
                                <!-- Activity Nature -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,12">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🎯" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="طبيعة النشاط: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitNature, FallbackValue='اضافة نشاط المهمة'}" Foreground="Black"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Visit Conductor -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,10">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="👤" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="القائم بالزيارة: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitConductor, FallbackValue=' '}" Foreground="Black"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>



                                <!-- Route and Message Frame -->
                                <Border Margin="0,5,0,5" BorderBrush="Black" BorderThickness="2" Background="White">
                                    <StackPanel>
                                        <!-- Message Header -->
                                        <Border Background="#E8F4FD" Padding="8,6">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                                <TextBlock Text="🗺️" FontSize="12" Margin="0,0,8,0" Foreground="Black" VerticalAlignment="Center"/>
                                                <TextBlock Text="خط السير ونص الرسالة المرسلة للسائقين" FontSize="12" FontWeight="Bold"
                                                         Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Message Content -->
                                        <Border Background="White" Padding="10,8">
                                            <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="8" Background="White">
                                                <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="16" Foreground="Black"
                                                         Text="{Binding ReportData.WinnerDriverMessage, FallbackValue='لم يتم حفظ نص الرسالة للسائق الفائز'}"
                                                         TextAlignment="Left" FontFamily="Segoe UI"/>
                                            </Border>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- Enhanced Dates Section in Single Row -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- Departure Date -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                            <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="تاريخ النزول:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.DepartureDate, FallbackValue='15/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Return Date -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="تاريخ العودة:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.ReturnDate, FallbackValue='17/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Days Count -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⏰" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="عدد الأيام:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.DaysCount, FallbackValue='3'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                            <TextBlock Text=" يوم" FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <!-- Simplified Notes Section -->
                                <StackPanel Orientation="Horizontal" Margin="0,8,0,10">
                                    <TextBlock Text="📝" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Center" MaxWidth="600">
                                        <Run Text="ملاحظات: " FontWeight="Bold" Foreground="Black"/>
                                        <Run Text="{Binding ReportData.Notes, FallbackValue='لا توجد ملاحظات إضافية'}" Foreground="Black"/>
                                    </TextBlock>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Compact Price Offers Table -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,0" Background="White">
                        <StackPanel>
                            <!-- Enhanced Header -->
                            <Border Background="#B8D4F0" Padding="10,8">
                                <TextBlock Text="قائمة الأسعار المقدمة من السائقين" FontWeight="Bold" FontSize="14"
                                         Foreground="Black" HorizontalAlignment="Center"/>
                            </Border>

                            <!-- Enhanced Table Header -->
                            <Border BorderBrush="Black" BorderThickness="2" Background="#F0F8FF">
                                <Grid MinHeight="35">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="60"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="80"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="الرقم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="اسم السائق" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="رقم التلفون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="السعر المقدم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="4" Padding="10">
                                        <TextBlock Text="الحالة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Data -->
                            <ItemsControl ItemsSource="{Binding ReportData.PriceOffers}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                            <Grid MinHeight="35" Background="White">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="60"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Serial Number -->
                                                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="Black"/>
                                                </Border>

                                                <!-- Driver Name -->
                                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding DriverName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="SemiBold" Foreground="Black"/>
                                                </Border>

                                                <!-- Phone Number -->
                                                <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding PhoneNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="12" FontFamily="Consolas" Foreground="Black"/>
                                                </Border>

                                                <!-- Price -->
                                                <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <TextBlock Text="{Binding OfferedPrice, StringFormat='{}{0:N0}'}"
                                                                 FontSize="13" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                        <TextBlock Text=" ريال" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                                    </StackPanel>
                                                </Border>

                                                <!-- Status -->
                                                <Border Grid.Column="4" Padding="10">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="Bold" VerticalAlignment="Center" Foreground="Black"/>
                                                        <TextBlock Text=" 🏆" FontSize="14" VerticalAlignment="Center" Margin="3,0,0,0">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding IsWinner}" Value="True">
                                                                            <Setter Property="Visibility" Value="Visible"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Border>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- Simplified Award Decision Section -->
                    <StackPanel Margin="0,0,0,5">
                        <!-- Decision Text and Driver Details in Single Section -->
                        <Border Background="White" Padding="10,5" Margin="0,0,0,0">
                            <StackPanel>
                                <!-- Decision Text in Single Line with Driver Name - No Wrapping -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,5">
                                    <TextBlock Text="بناءً على الأسعار المقدمة من الإخوة أصحاب المركبات الذي تم التواصل معهم أعلاه، فقد تم إرساء النقل على الأخ" FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                    <TextBlock Text=" " FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold" FontSize="14" Foreground="Black" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Winner Driver Details in Distinguished Frame - Same Line -->
                                <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="8" Padding="15,10" Margin="0,0,0,0">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- ID -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="🆔" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="رقم البطاقة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.NationalId}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Vehicle Type -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="🚙" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="نوع السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleType}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Capacity -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="⚖️" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="قدرة السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleCapacity}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Year -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📅" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="سنة الصنع:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>
                    </StackPanel>


                    <!-- Professional Signatures Section -->
                    <Border Background="White" Padding="15,12" Margin="0,20,0,8">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Task Manager -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="المكلف بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" TextWrapping="Wrap" MaxWidth="180" Foreground="Black" LineHeight="18"
                                             Text="{Binding ReportData.VisitConductorFormatted, Mode=OneWay, FallbackValue='جمال علي عبدالله الفاطمي &amp; أحمد صالح أحمد حميد'}"/>
                                </StackPanel>

                                <!-- Movement Responsible -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="مسئول الحركة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock Text="علي علي العمدي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                </StackPanel>

                                <!-- Branch Manager -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="يعتمد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                    <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="11" FontStyle="Italic" Foreground="Black" Margin="0,4,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- رقم الصفحة الأولى -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="1" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الثانية: عقد إيجار السيارة (الجزء الأول) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="20">
                    <!-- رأس العقد المبسط -->
                    <Grid Margin="0,0,0,25">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- شعار المؤسسة بالشمال -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                            <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock Text="Social Fund for Development" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <!-- عنوان العقد بالوسط -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="عقد اتفاق" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                            <TextBlock Text="إيجار سيارة مع سائقها" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>

                        <!-- رقم العقد ورقم الزيارة باليمين -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                            <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                            <TextBlock Text="{Binding ReportData.VisitNumber, StringFormat='رقم الزيارة: {0}'}" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>

                    <!-- مقدمة العقد من قاعدة البيانات -->
                    <TextBlock FontSize="13" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Margin="0,0,0,20"
                               Text="{Binding ContractTemplate.ContractIntroduction}"/>

                    <!-- أطراف العقد -->
                    <StackPanel Margin="0,0,0,20">
                        <!-- الطرف الأول -->
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="الطرف الأول (مالك السيارة والسائق)" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify"
                                       Text="{Binding ContractTemplate.FirstPartyTemplate}"/>
                        </StackPanel>

                        <!-- الطرف الثاني -->
                        <StackPanel>
                            <TextBlock Text="الطرف الثاني (الصندوق الاجتماعي للتنمية)" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify"
                                       Text="{Binding ContractTemplate.SecondPartyTemplate}"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- أولاً: مواصفات السيارة -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="أولاً: مواصفات السيارة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Margin="0,0,0,10">
                            <Run Text="أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية:"/>
                        </TextBlock>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="نوع السيارة: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleType}" FontWeight="Bold"/>
                            <Run Text="، سنة الصنع: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontWeight="Bold"/>
                            <Run Text="، لون السيارة: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleColor}" FontWeight="Bold"/>
                            <Run Text="، رقم اللوحة: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                            <Run Text="، رقم رخصة التسيير: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.LicenseNumber}" FontWeight="Bold"/>
                            <Run Text="، تاريخ إصدار الرخصة: "/>
                            <Run Text="{Binding ReportData.WinnerDriver.LicenseIssueDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                        </TextBlock>
                    </StackPanel>

                    <!-- ثانياً: غرض الانتفاع -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="ثانياً: غرض الانتفاع" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/ "/>
                            <Run Text="{Binding ReportData.VisitConductor}" FontWeight="Bold"/>
                            <Run Text="، صفته "/>
                            <Run Text="{Binding ReportData.VisitConductorRank}" FontWeight="Bold"/>
                            <Run Text=" أثناء تنفيذ المهام الميدانية المطلوبة منه في إطار عمل الصندوق الاجتماعي للتنمية."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- ثالثاً: المدة الإيجارية -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="ثالثاً: المدة الإيجارية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="تاريخ بداية المهمة: "/>
                            <Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold"/>
                            <Run Text=" الموافق "/>
                            <Run Text="{Binding ReportData.StartDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                            <Run Text="م، تاريخ انتهاء المهمة: "/>
                            <Run Text="{Binding ReportData.EndDateArabic}" FontWeight="Bold"/>
                            <Run Text=" الموافق "/>
                            <Run Text="{Binding ReportData.EndDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold"/>
                            <Run Text="م، إجمالي عدد الأيام: "/>
                            <Run Text="{Binding ReportData.DaysCount}" FontWeight="Bold"/>
                            <Run Text=" أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- رابعاً: القيمة الإيجارية -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="رابعاً: القيمة الإيجارية (الأجرة)" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره "/>
                            <Run Text="{Binding ReportData.WinnerDriver.TotalPrice}" FontWeight="Bold"/>
                            <Run Text=" ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- خامساً: إقرار الملكية -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="خامساً: إقرار الملكية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- سادساً: التزامات الطرف الأول -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="سادساً: التزامات الطرف الأول" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify">
                            <Run Text="يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- رقم الصفحة الثانية -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="2" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الثالثة: عقد إيجار السيارة (الجزء الثاني) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="20">

                    <!-- رأس الصفحة الثالثة -->
                    <Grid Margin="0,0,0,25">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Side - Contract Info -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="رقم العقد: 2025001" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                            <TextBlock Text="رقم الأرشيف: 911-13031" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333" Margin="0,2,0,0"/>
                        </StackPanel>

                        <!-- Center - Title -->
                        <Border Grid.Column="1" BorderBrush="#8B4513" BorderThickness="2" Padding="20,10" HorizontalAlignment="Center">
                            <TextBlock Text="تابع عقد اتفاق" FontSize="18" FontWeight="Bold"
                                     HorizontalAlignment="Center" Foreground="#8B4513" TextAlignment="Center"/>
                        </Border>

                        <!-- Right Side - Organization -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                            <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333" Margin="0,2,0,0"/>
                            <TextBlock Text="Social Fund for Development" FontSize="10" HorizontalAlignment="Center" Foreground="#666666"/>
                        </StackPanel>
                    </Grid>

                    <!-- Subtitle -->
                    <Border BorderBrush="#D2691E" BorderThickness="2" Padding="15,8" Margin="0,0,0,25" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🚗" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                            <TextBlock Text="إيجــــــــــــــار سيــــــــــــــارة" FontSize="16" FontWeight="Bold" Foreground="#D2691E" VerticalAlignment="Center"/>
                            <TextBlock Text="🚗" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- سابعاً: التزامات الطرف الثاني -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="سابعاً: التزامات الطرف الثاني" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,10"/>

                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,10">
                            <Run Text="يقر الطرف الثاني بأنه عاين كافة الوثائق المطلوبة في وسيلة النقل ووجد أنها مستوفية لكافة لوازمها ولذلك يلتزم بموجب هذا العقد بما يلي:"/>
                        </TextBlock>

                        <!-- Obligation Items -->
                        <StackPanel Margin="20,0,0,0">
                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,5">
                                <Run Text="أ" FontWeight="Bold"/>
                                <Run Text="   التزام ممثليه بتعليمات سائق وسيلة النقل."/>
                            </TextBlock>

                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,5">
                                <Run Text="ب" FontWeight="Bold"/>
                                <Run Text="   التزام ممثليه بوقت وموعد الرحلة."/>
                            </TextBlock>

                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black">
                                <Run Text="ت" FontWeight="Bold"/>
                                <Run Text="   تبليغ السائق على الفور عند إلغاء أو قطع أو تمديد مهمة العمل بدون إنذار مسبق."/>
                            </TextBlock>
                        </StackPanel>
                    </StackPanel>

                    <!-- ثامناً: تسوية وحل المنازعات -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="ثامناً: تسوية وحل المنازعات" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black">
                            <Run Text="كل نزاع ينشأ بين الطرفين أو خلفهما يتعلق بانعقاد أو تنفيذ أو تفسير هذا العقد، أو ما يتفرع عنه أو يرتبط به بأي وجه من الوجوه يتم حله وتسويته بينهما أولاً بالطرق الودية خلال "/>
                            <Run Text="تسعين يوماً" FontWeight="Bold"/>
                            <Run Text="."/>
                        </TextBlock>
                    </StackPanel>

                    <!-- تاسعاً: أحكام أخرى -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="تاسعاً: أحكام أخرى" FontSize="14" FontWeight="Bold" Foreground="Black" Margin="0,0,0,10"/>

                        <!-- Provision Items -->
                        <StackPanel Margin="20,0,0,0">
                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,5">
                                <Run Text="⏰" FontWeight="Bold"/>
                                <Run Text="   ينتهي العقد بانتهاء مدته المتفق عليها في العقد دون الحاجة إلى تنبيه أو إنذار ما لم يبلغ الطرف الثاني الطرف الأول برغبته في تمديد المدة."/>
                            </TextBlock>

                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black" Margin="0,0,0,5">
                                <Run Text="🛡️" FontWeight="Bold"/>
                                <Run Text="   الطرف الأول (مالك السيارة) المسؤول وحده على التأمين على نفسه وعلى سيارته من كافة الحوادث والاعتداءات من الغير، ولا يتحمل الصندوق أي مسؤولية تجاه ما قد يتعرض له أثناء تنفيذ المهمة."/>
                            </TextBlock>

                            <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="Black">
                                <Run Text="⚠️" FontWeight="Bold"/>
                                <Run Text="   يتحمل الطرف الأول (مالك السيارة والسائق) وحده مسئولية تعويض الراكب عن أي ضرر قد يحدث له نتيجة إصابته بسبب وقوع حادث للسيارة."/>
                            </TextBlock>
                        </StackPanel>
                    </StackPanel>

                    <!-- خاتمة العقد -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="خاتمة العقد" FontWeight="Bold" FontSize="14" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Center" Foreground="Black">
                            <Run Text="حرر هذا العقد من "/>
                            <Run Text="ثلاث نسخ" FontWeight="Bold"/>
                            <Run Text=" بيد كل طرف نسخة منه، والنسخة الثالثة للتوثيق."/>
                        </TextBlock>
                    </StackPanel>

                    <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Margin="0,0,0,25">
                        <Run Text="🤲 ولله الأمر كله وهو على كل شيء شهيد 🤲"/>
                    </TextBlock>

                    <!-- توقيعات أطراف العقد -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="توقيعات أطراف العقد" FontWeight="Bold" FontSize="14"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- First Party Signature -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="5">
                                <TextBlock Text="الطرف الأول" FontWeight="Bold" FontSize="13" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,3"/>
                                <TextBlock Text="(مالك السيارة)" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,15"/>
                                <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Margin="0,0,0,10"/>
                                <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black" Margin="0,0,0,8"/>
                                <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber}" HorizontalAlignment="Center" FontSize="10" Foreground="#666666"/>
                            </StackPanel>

                            <!-- Second Party Signature -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="5">
                                <TextBlock Text="الطرف الثاني" FontWeight="Bold" FontSize="13" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,3"/>
                                <TextBlock Text="القائم بالمهمة" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#666666" Margin="0,0,0,15"/>
                                <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Margin="0,0,0,10"/>
                                <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black" Margin="0,0,0,8"/>
                                <TextBlock Text="عبدالله علي ناصر الأصرعي" HorizontalAlignment="Center" FontSize="10" FontWeight="Bold" Foreground="#666666"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!-- الاعتمادات الإدارية -->
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="الاعتمادات الإدارية" FontWeight="Bold" FontSize="14"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Branch Manager -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="5">
                                <TextBlock Text="يعتمد الطرف الثاني" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,15"/>
                                <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Margin="0,0,0,10"/>
                                <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black" Margin="0,0,0,5"/>
                                <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="10" FontStyle="Italic" Foreground="#666666"/>
                            </StackPanel>

                            <!-- Service Provider -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="5">
                                <TextBlock Text="مزود الخدمة" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,15"/>
                                <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="12" Margin="0,0,0,10"/>
                                <TextBlock Text="علي أحمد محمد المعطم" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!-- رقم الصفحة الثالثة -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="3" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الرابعة: بيان وإقرار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="20">

                    <!-- رأس الصفحة الرابعة -->
                    <Grid Margin="0,0,0,25">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Left Side - Contract Info -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="رقم العقد: 2025001" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                            <TextBlock Text="رقم الأرشيف: 911-13031" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333" Margin="0,2,0,0"/>
                        </StackPanel>

                        <!-- Center - Title -->
                        <Border Grid.Column="1" BorderBrush="#6F42C1" BorderThickness="2" Padding="20,10" HorizontalAlignment="Center">
                            <TextBlock Text="بيان وإقرار" FontSize="18" FontWeight="Bold"
                                     HorizontalAlignment="Center" Foreground="#6F42C1" TextAlignment="Center"/>
                        </Border>

                        <!-- Right Side - Organization -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                            <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333" Margin="0,2,0,0"/>
                            <TextBlock Text="Social Fund for Development" FontSize="10" HorizontalAlignment="Center" Foreground="#666666"/>
                        </StackPanel>
                    </Grid>

                    <!-- Subtitle -->
                    <Border BorderBrush="#6F42C1" BorderThickness="2" Padding="15,8" Margin="0,0,0,25" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📝" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                            <TextBlock Text="تضارب المصالح والعلاقات العائلية" FontSize="16" FontWeight="Bold" Foreground="#6F42C1" VerticalAlignment="Center"/>
                            <TextBlock Text="📝" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- إقرار مزود الخدمة -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="إقرار مزود الخدمة" FontWeight="Bold" FontSize="14" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black">
                            <Run Text="إقراراً مزود الخدمة صاحب سيارة رقم "/>
                            <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold"/>
                            <Run Text=" بأنني تسلمت من الصندوق الاجتماعي للتنمية نسخة من "/>
                            <Run Text="مدونة تضارب المصالح" FontWeight="Bold"/>
                            <Run Text="، وخصوص عقدي بلدى العاملين في الصندوق فإنني أقر بما يلي:"/>
                        </TextBlock>
                    </StackPanel>

                    <!-- القسم الأول: العلاقات العائلية -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="القسم الأول: العلاقات العائلية" FontWeight="Bold" FontSize="14"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <TextBlock FontSize="12" Foreground="Black" Margin="0,0,0,10">
                            <Run Text="1) أنه "/>
                            <Run Text="ليس لديه علاقة عائلية" FontWeight="Bold"/>
                            <Run Text=" مع أحد العاملين    ☐ نعم"/>
                        </TextBlock>

                        <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="11" FontWeight="Bold" Foreground="Black" Margin="0,0,0,15"/>

                        <!-- Employee Details Table -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Table Headers -->
                            <TextBlock Grid.Column="0" Text="اسم العامل" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            <TextBlock Grid.Column="1" Text="فرع الصندوق" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            <TextBlock Grid.Column="2" Text="نوع العلاقة العائلية" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                        </Grid>

                        <!-- Empty Rows for Data Entry -->
                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>

                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>
                    </StackPanel>

                    <!-- القسم الثاني: المصالح التجارية -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="القسم الثاني: المصالح التجارية" FontWeight="Bold" FontSize="14"
                                 Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <TextBlock FontSize="12" Foreground="Black" Margin="0,0,0,10">
                            <Run Text="2) أنه "/>
                            <Run Text="مصالح مباشرة أو غير مباشرة" FontWeight="Bold"/>
                            <Run Text=" مع أحد العاملين    ☐ نعم    ☐ لا"/>
                        </TextBlock>

                        <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="11" FontWeight="Bold" Foreground="Black" Margin="0,0,0,15"/>

                        <!-- Second Table -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Table Headers -->
                            <TextBlock Grid.Column="0" Text="اسم العامل" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            <TextBlock Grid.Column="1" Text="فرع الصندوق" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                            <TextBlock Grid.Column="2" Text="نوع المصلحة" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,10"/>
                        </Grid>

                        <!-- Empty Rows -->
                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>

                        <Grid Margin="0,0,0,5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="1" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                            <TextBlock Grid.Column="2" Text="____________________" HorizontalAlignment="Center" FontSize="11"/>
                        </Grid>
                    </StackPanel>

                    <!-- الإقرار النهائي -->
                    <StackPanel Margin="0,20,0,15">
                        <TextBlock Text="الإقرار النهائي" FontWeight="Bold" FontSize="14" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="Black">
                            <Run Text="أنا الموقع أدناه أقر بأن جميع البيانات المذكورة أعلاه "/>
                            <Run Text="صحيحة" FontWeight="Bold"/>
                            <Run Text="، وبحق للصندوق اتخاذ الإجراءات التي يراها مناسبة تجاهي في حال عدم صحة البيانات المذكورة."/>
                        </TextBlock>
                    </StackPanel>

                    <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="Black" Margin="0,0,0,25">
                        <Run Text="🤲 والله الموفق 🤲"/>
                    </TextBlock>

                    <!-- توقيع مزود الخدمة -->
                    <StackPanel Margin="0,0,0,0">
                        <TextBlock Text="توقيع مزود الخدمة" FontSize="14" FontWeight="Bold" Foreground="Black" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                        <StackPanel HorizontalAlignment="Center">
                            <!-- Name Section -->
                            <StackPanel Margin="0,0,0,20">
                                <TextBlock Text="الاسم:" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,8"/>
                                <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontSize="14" FontWeight="Bold"
                                         HorizontalAlignment="Center" Foreground="Black"/>
                            </StackPanel>

                            <!-- Signature Section -->
                            <StackPanel>
                                <TextBlock Text="التوقيع:" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,15"/>
                                <TextBlock Text="____________________" HorizontalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <!-- رقم الصفحة الرابعة -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="4" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
