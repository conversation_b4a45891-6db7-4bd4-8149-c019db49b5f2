using System;
using System.Threading.Tasks;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;
using System.Collections.Generic;
using System.Linq;

namespace DriverManagementSystem
{
    /// <summary>
    /// فئة اختبار نظام العروض
    /// </summary>
    public class TestOffersSystem
    {
        /// <summary>
        /// اختبار حفظ وجلب العروض
        /// </summary>
        public static async Task TestSaveAndLoadOffers()
        {
            try
            {
                Console.WriteLine("🧪 بدء اختبار نظام العروض...");

                using var context = new ApplicationDbContext();
                var offersService = new OffersService(context);

                // إنشاء عروض تجريبية
                var testOffers = new List<DriverOffer>
                {
                    new DriverOffer
                    {
                        DriverId = 1,
                        DriverName = "فيصل حميد أحمد الطيبي",
                        DriverCode = "D001",
                        PhoneNumber = "777597143",
                        VehicleType = "هايلكس",
                        VehicleNumber = "ق ع ذ 20000",
                        VisitNumber = "15/1/2025",
                        DaysCount = 4,
                        ProposedAmount = 20000,
                        IsWinner = true,
                        IsSelected = true
                    },
                    new DriverOffer
                    {
                        DriverId = 2,
                        DriverName = "مجاهد سنان صالح عبيد",
                        DriverCode = "D002",
                        PhoneNumber = "773731517",
                        VehicleType = "هايلكس",
                        VehicleNumber = "ق ع ذ 30000",
                        VisitNumber = "15/1/2025",
                        DaysCount = 4,
                        ProposedAmount = 30000,
                        IsWinner = false,
                        IsSelected = false
                    },
                    new DriverOffer
                    {
                        DriverId = 3,
                        DriverName = "حسين عبده أحمد صالح النساء",
                        DriverCode = "D003",
                        PhoneNumber = "773530381",
                        VehicleType = "هايلكس",
                        VehicleNumber = "ق ع ذ 50000",
                        VisitNumber = "15/1/2025",
                        DaysCount = 4,
                        ProposedAmount = 50000,
                        IsWinner = false,
                        IsSelected = false
                    }
                };

                // حفظ العروض
                Console.WriteLine("💾 حفظ العروض التجريبية...");
                var saveResult = await offersService.SaveVisitOffersAsync(testOffers, "15/1/2025", 4);
                Console.WriteLine($"✅ نتيجة الحفظ: {saveResult}");

                // جلب العروض
                Console.WriteLine("🔍 جلب العروض المحفوظة...");
                var loadedOffers = await offersService.GetVisitOffersByNumberAsync("15/1/2025");
                Console.WriteLine($"📊 تم جلب {loadedOffers.Count} عرض");

                // عرض تفاصيل العروض المجلبة
                foreach (var offer in loadedOffers)
                {
                    Console.WriteLine($"   - {offer.DriverName}: {offer.FormattedAmount} (فائز: {offer.IsWinner})");
                }

                // اختبار زيارة أخرى
                Console.WriteLine("\n🧪 اختبار زيارة أخرى...");
                var testOffers2 = new List<DriverOffer>
                {
                    new DriverOffer
                    {
                        DriverId = 4,
                        DriverName = "سائق تجريبي 1",
                        DriverCode = "D004",
                        PhoneNumber = "777000001",
                        VehicleType = "باص",
                        VehicleNumber = "ق ع ذ 60000",
                        VisitNumber = "16/1/2025",
                        DaysCount = 3,
                        ProposedAmount = 45000,
                        IsWinner = true,
                        IsSelected = true
                    }
                };

                await offersService.SaveVisitOffersAsync(testOffers2, "16/1/2025", 3);
                var loadedOffers2 = await offersService.GetVisitOffersByNumberAsync("16/1/2025");
                Console.WriteLine($"📊 تم جلب {loadedOffers2.Count} عرض للزيارة الثانية");

                // التأكد من عدم التداخل
                var offersForVisit1 = await offersService.GetVisitOffersByNumberAsync("15/1/2025");
                Console.WriteLine($"🔍 الزيارة الأولى لا تزال تحتوي على {offersForVisit1.Count} عرض");

                Console.WriteLine("✅ اكتمل اختبار النظام بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// اختبار حذف العروض
        /// </summary>
        public static async Task TestDeleteOffers()
        {
            try
            {
                Console.WriteLine("🧪 اختبار حذف العروض...");

                using var context = new ApplicationDbContext();
                var offersService = new OffersService(context);

                // حذف عروض زيارة معينة
                var deleteResult = await offersService.DeleteVisitOffersAsync("15/1/2025");
                Console.WriteLine($"🗑️ نتيجة الحذف: {deleteResult}");

                // التحقق من الحذف
                var remainingOffers = await offersService.GetVisitOffersByNumberAsync("15/1/2025");
                Console.WriteLine($"📊 العروض المتبقية: {remainingOffers.Count}");

                Console.WriteLine("✅ اكتمل اختبار الحذف!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار الحذف: {ex.Message}");
            }
        }
    }
}
