using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Documents;
using System.Windows.Media;

namespace SFDSystem.Helpers
{
    /// <summary>
    /// DocumentPaginator مخصص لطباعة صفحات متعددة منفصلة
    /// </summary>
    public class MultiPageDocumentPaginator : DocumentPaginator
    {
        private readonly List<FrameworkElement> _pages;
        private readonly Size _pageSize;

        public MultiPageDocumentPaginator(List<System.Windows.Controls.Border> pages)
        {
            _pages = pages?.Cast<FrameworkElement>().ToList() ?? throw new ArgumentNullException(nameof(pages));
            
            // حجم صفحة A4 بالبكسل (96 DPI)
            _pageSize = new Size(794, 1123); // 210mm x 297mm at 96 DPI
        }

        public override DocumentPage GetPage(int pageNumber)
        {
            try
            {
                if (pageNumber < 0 || pageNumber >= _pages.Count)
                {
                    return DocumentPage.Missing;
                }

                var page = _pages[pageNumber];
                
                System.Diagnostics.Debug.WriteLine($"📄 إنشاء صفحة PDF رقم {pageNumber + 1}");

                // التأكد من أن العنصر معروض بشكل صحيح
                if (page.Parent != null)
                {
                    // إنشاء نسخة من الصفحة للطباعة
                    page = ClonePage(page);
                }

                // تحديد حجم الصفحة
                page.Measure(_pageSize);
                page.Arrange(new Rect(_pageSize));

                // إنشاء DrawingVisual للصفحة
                var visual = new DrawingVisual();
                using (var context = visual.RenderOpen())
                {
                    // رسم خلفية بيضاء
                    context.DrawRectangle(Brushes.White, null, new Rect(_pageSize));
                    
                    // رسم محتوى الصفحة
                    var visualBrush = new VisualBrush(page)
                    {
                        Stretch = Stretch.Uniform,
                        AlignmentX = AlignmentX.Center,
                        AlignmentY = AlignmentY.Top
                    };
                    
                    context.DrawRectangle(visualBrush, null, new Rect(_pageSize));
                }

                return new DocumentPage(visual, _pageSize, new Rect(_pageSize), new Rect(_pageSize));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة {pageNumber + 1}: {ex.Message}");
                return DocumentPage.Missing;
            }
        }

        private FrameworkElement ClonePage(FrameworkElement originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة للطباعة
                var xaml = System.Windows.Markup.XamlWriter.Save(originalPage);
                var clonedPage = (FrameworkElement)System.Windows.Markup.XamlReader.Parse(xaml);
                
                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في نسخ الصفحة، سيتم استخدام الأصلية: {ex.Message}");
                return originalPage;
            }
        }

        public override bool IsPageCountValid => true;

        public override int PageCount => _pages?.Count ?? 0;

        public override Size PageSize
        {
            get => _pageSize;
            set { /* حجم الصفحة ثابت */ }
        }

        public override IDocumentPaginatorSource Source => null;
    }
}
