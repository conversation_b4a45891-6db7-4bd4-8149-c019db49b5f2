# 📞 نظام ربط أرقام التلفون للسائقين مع التقارير

## 🎯 الهدف
تحسين نظام التقارير ليجلب أرقام التلفون من جدول السائقين الرئيسي بدلاً من الاعتماد على البيانات المحفوظة في جدول العروض فقط.

## 🔧 التحسينات المضافة

### 1. خدمة ربط البيانات الجديدة
```csharp
// SFD/Services/DriverDataService.cs
public class DriverDataService
{
    // جلب العروض مع ربطها بجدول السائقين
    public async Task<List<PriceOfferItem>> GetEnhancedPriceOffersAsync(string visitNumber)
    
    // مزامنة البيانات بين الجداول
    public async Task<bool> SynchronizeDriverDataAsync()
    
    // تحديث رقم التلفون في جدول العروض
    public async Task<bool> UpdateDriverPhoneInQuotesAsync(string driverName, string phoneNumber)
}
```

### 2. تحسين خدمة قاعدة البيانات
```csharp
// SFD/Services/SqliteDataService.cs
public async Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId)
{
    // البحث عن السائق في جدول السائقين الرئيسي لجلب رقم التلفون
    var driver = await _context.Drivers
        .FirstOrDefaultAsync(d => d.Name == offer.DriverName || 
                                 d.DriverCode == offer.DriverCode ||
                                 d.Id == offer.DriverId);

    // استخدام رقم التلفون من جدول السائقين إذا وُجد
    var phoneNumber = driver?.PhoneNumber ?? offer.PhoneNumber ?? "غير محدد";
}
```

### 3. تحسين ViewModel التقارير
```csharp
// SFD/ViewModels/ReportViewModel.cs
private readonly DriverDataService _driverDataService;

private async Task LoadDriversAndPricesData(FieldVisit visit)
{
    // استخدام الطريقة المحسنة
    var priceOffers = await _driverDataService.GetEnhancedPriceOffersAsync(visit.VisitNumber);
    
    // طريقة بديلة في حالة عدم وجود بيانات
    if (!priceOffers?.Any() == true)
    {
        var fallbackOffers = await _databaseService.GetPriceOffersByVisitIdAsync(visit.Id);
    }
}
```

## 📊 آلية العمل

### 1. البحث المتعدد المعايير
```csharp
private Driver FindDriverByMultipleCriteria(List<Driver> allDrivers, DriverQuote quote)
{
    // البحث بالاسم الكامل
    var driver = allDrivers.FirstOrDefault(d => 
        string.Equals(d.Name.Trim(), quote.DriverName.Trim(), StringComparison.OrdinalIgnoreCase));

    // البحث بكود السائق
    if (driver == null && !string.IsNullOrEmpty(quote.DriverCode))
    {
        driver = allDrivers.FirstOrDefault(d => d.DriverCode == quote.DriverCode);
    }

    // البحث بالمعرف
    if (driver == null && quote.DriverId > 0)
    {
        driver = allDrivers.FirstOrDefault(d => d.Id == quote.DriverId);
    }

    return driver;
}
```

### 2. أولوية رقم التلفون
```csharp
private string GetDriverPhoneNumber(Driver driver, DriverQuote quote)
{
    // أولوية للرقم من جدول السائقين الرئيسي
    if (driver != null && !string.IsNullOrWhiteSpace(driver.PhoneNumber))
    {
        return driver.PhoneNumber;
    }

    // إذا لم يوجد في الجدول الرئيسي، استخدم المحفوظ في العرض
    if (!string.IsNullOrWhiteSpace(quote.PhoneNumber))
    {
        return quote.PhoneNumber;
    }

    return "غير محدد";
}
```

## 🚀 كيفية الاستخدام

### 1. في التقارير
```csharp
// سيتم جلب أرقام التلفون تلقائياً من جدول السائقين
var report = new ReportViewModel();
await report.LoadFieldVisitsAsync();
// اختيار زيارة
report.SelectedVisit = visit;
// سيتم جلب العروض مع أرقام التلفون الصحيحة تلقائياً
```

### 2. مزامنة البيانات
```csharp
var driverDataService = new DriverDataService(dbContext);
await driverDataService.SynchronizeDriverDataAsync();
```

### 3. تحديث رقم تلفون محدد
```csharp
await driverDataService.UpdateDriverPhoneInQuotesAsync("أحمد محمد", "777123456");
```

## 📋 مثال على النتيجة النهائية

### قبل التحسين:
```
| الرقم | اسم السائق | رقم التلفون | السعر المقدم | الحالة |
|------|------------|-------------|-------------|--------|
|   1  | أحمد محمد  | غير محدد    |      50,000 | مقبول |
|   2  | علي أحمد   | 777123456   |      45,000 | مرفوض |
```

### بعد التحسين:
```
| الرقم | اسم السائق | رقم التلفون | السعر المقدم | الحالة |
|------|------------|-------------|-------------|--------|
|   1  | أحمد محمد  | 777654321   |      50,000 | مقبول |
|   2  | علي أحمد   | 777123456   |      45,000 | مرفوض |
```

## 🔍 التشخيص والمراقبة

### رسائل التشخيص
```csharp
System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {priceOffers.Count} عرض سعر للزيارة {visit.VisitNumber}");
System.Diagnostics.Debug.WriteLine($"🔄 بدء مزامنة بيانات السائقين...");
System.Diagnostics.Debug.WriteLine($"⚠️ No driver found for contract: {visit.DriverContract}");
```

### إحصائيات النظام
```csharp
var stats = await driverDataService.GetDriverStatisticsAsync();
Console.WriteLine($"📊 إجمالي السائقين: {stats.TotalDrivers}");
Console.WriteLine($"📊 السائقين بأرقام تلفون: {stats.DriversWithPhones}");
Console.WriteLine($"📊 نسبة اكتمال أرقام التلفون: {stats.PhoneCompletionRate:F1}%");
```

## ⚙️ الإعدادات والتكوين

### 1. إضافة الخدمة للنظام
```csharp
// في ReportViewModel
private readonly DriverDataService _driverDataService;

public ReportViewModel()
{
    _driverDataService = new DriverDataService(new Data.ApplicationDbContext());
}
```

### 2. التحقق من اكتمال البيانات
```csharp
public async Task ValidateDriverData()
{
    var drivers = await _dataService.GetDriversAsync();
    var driversWithoutPhone = drivers.Where(d => string.IsNullOrWhiteSpace(d.PhoneNumber)).ToList();
    
    if (driversWithoutPhone.Any())
    {
        Console.WriteLine($"⚠️ يوجد {driversWithoutPhone.Count} سائق بدون رقم تلفون");
    }
}
```

## 🎯 الفوائد

1. **دقة البيانات**: ضمان عرض أرقام التلفون الصحيحة في التقارير
2. **التزامن**: مزامنة البيانات بين جدول السائقين وجدول العروض
3. **المرونة**: البحث بعدة معايير للعثور على السائق المطابق
4. **الموثوقية**: طريقة بديلة في حالة عدم وجود بيانات
5. **سهولة الصيانة**: كود منظم وقابل للتطوير

## 🔧 الصيانة والتطوير

### إضافة معايير بحث جديدة
```csharp
// يمكن إضافة معايير بحث إضافية في FindDriverByMultipleCriteria
// مثل البحث برقم التلفون أو رقم السيارة
```

### تحسين الأداء
```csharp
// يمكن إضافة Cache للسائقين لتحسين الأداء
private static List<Driver> _cachedDrivers;
private static DateTime _lastCacheUpdate;
```

## 📞 الدعم الفني

للمساعدة أو الاستفسارات حول النظام، يرجى مراجعة:
- ملف `DriverPhoneIntegrationExample.cs` للأمثلة العملية
- رسائل التشخيص في Debug Output
- إحصائيات النظام للتحقق من صحة البيانات
