# 📊 دليل اختبار استيراد Excel

## 🎯 الهدف
اختبار وظيفة استيراد البيانات من ملفات Excel في النظام.

## 📋 متطلبات ملف Excel للاختبار

### 1. الشيتات المطلوبة:
- **نموذج استمارة الزيارة الميدانية** (الشيت الرئيسي)
- **project_repeat** (شيت المشاريع)
- **day_plan_repeat** (شيت خط السير)

### 2. أعمدة الشيت الرئيسي:
- `_index` - الرقم المرجعي (مثل: 1, 2, 3)
- `visit_form_number` - رقم الزيارة (مثل: V2025001)
- `field_days_count` - عدد الأيام (مثل: 5)
- `start_date` - تاريخ البداية
- `end_date` - تاريخ النهاية
- `sector` - كود القطاع
- `visitors` - عدد الزوار
- `trip_purpose` - مهمة النزول
- `security_route` - المسار الأمني

### 3. أعمدة شيت المشاريع (project_repeat):
- `_parent_index` - الرقم المرجعي للزيارة
- `project_code` - رقم المشروع
- `project_name` - اسم المشروع
- `project_days` - عدد أيام المشروع

### 4. أعمدة شيت خط السير (day_plan_repeat):
- `_parent_index` - الرقم المرجعي للزيارة
- `day_num` - رقم اليوم
- `day_plan` - خط السير لليوم

## 🔧 خطوات الاختبار:

### 1. إنشاء ملف Excel تجريبي:
```
الشيت الرئيسي:
_index | visit_form_number | field_days_count | start_date | end_date | sector | visitors | trip_purpose | security_route
1      | V2025001         | 3               | 2025-01-01 | 2025-01-03 | SEC001 | 2       | مراجعة مشاريع | طريق آمن

شيت المشاريع:
_parent_index | project_code | project_name | project_days
1            | PRJ001       | مشروع الطرق  | 2
1            | PRJ002       | مشروع المياه | 1

شيت خط السير:
_parent_index | day_num | day_plan
1            | 1       | زيارة موقع المشروع الأول
1            | 2       | مراجعة التقدم
1            | 3       | إعداد التقرير النهائي
```

### 2. تشغيل النظام:
1. افتح النظام
2. اذهب إلى صفحة "جديد"
3. أدخل الرقم المرجعي (1) في الحقل الرقمي
4. اضغط على زر "إدراج ملف Excel"
5. اختر ملف Excel التجريبي

### 3. التحقق من النتائج:
- يجب أن تتعبأ جميع الحقول تلقائياً
- رقم الزيارة: V2025001
- عدد الأيام: 3
- المشاريع: 2 مشروع
- خط السير: 3 أيام

## ✅ النتائج المتوقعة:
- ✅ تم إضافة مكتبة ClosedXML
- ✅ تم إنشاء ExcelImportService
- ✅ تم إضافة FolderMemoryService
- ✅ تم إضافة وظائف GetSectorByCodeAsync و GetOfficerByCodeAsync
- ✅ تم ربط استيراد Excel بالواجهة
- ✅ تم إضافة تعبئة الحقول من البيانات المستوردة
- ✅ النظام يبني ويعمل بنجاح

## 🎉 الخلاصة:
النظام جاهز الآن لاستيراد البيانات من ملفات Excel وتعبئة جميع الحقول تلقائياً!
