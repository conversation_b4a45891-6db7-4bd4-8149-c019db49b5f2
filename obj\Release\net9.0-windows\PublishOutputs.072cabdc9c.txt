C:\Users\<USER>\Desktop\22222\SFDSystem.exe
C:\Users\<USER>\Desktop\22222\SFDSystem.dll
C:\Users\<USER>\Desktop\22222\SFDSystem.runtimeconfig.json
C:\Users\<USER>\Desktop\22222\SFDSystem.pdb
C:\Users\<USER>\Desktop\22222\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Desktop\22222\ClosedXML.dll
C:\Users\<USER>\Desktop\22222\ClosedXML.Parser.dll
C:\Users\<USER>\Desktop\22222\DocumentFormat.OpenXml.dll
C:\Users\<USER>\Desktop\22222\DocumentFormat.OpenXml.Framework.dll
C:\Users\<USER>\Desktop\22222\ExcelNumberFormat.dll
C:\Users\<USER>\Desktop\22222\iTextSharp.LGPLv2.Core.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\Desktop\22222\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Desktop\22222\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Desktop\22222\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\22222\Microsoft.Xaml.Behaviors.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.Charting.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.Quality.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.Snippets.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.System.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.WPFonts.dll
C:\Users\<USER>\Desktop\22222\PdfSharp.dll
C:\Users\<USER>\Desktop\22222\Prism.Container.Abstractions.dll
C:\Users\<USER>\Desktop\22222\Prism.dll
C:\Users\<USER>\Desktop\22222\Prism.Events.dll
C:\Users\<USER>\Desktop\22222\Prism.Wpf.dll
C:\Users\<USER>\Desktop\22222\RBush.dll
C:\Users\<USER>\Desktop\22222\SixLabors.Fonts.dll
C:\Users\<USER>\Desktop\22222\SkiaSharp.dll
C:\Users\<USER>\Desktop\22222\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\Desktop\22222\SQLitePCLRaw.core.dll
C:\Users\<USER>\Desktop\22222\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\Desktop\22222\de\PdfSharp.Charting.resources.dll
C:\Users\<USER>\Desktop\22222\de\PdfSharp.resources.dll
C:\Users\<USER>\Desktop\22222\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\Desktop\22222\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\22222\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\22222\runtimes\win-x86\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\22222\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
C:\Users\<USER>\Desktop\22222\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-musl-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-ppc64le\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\22222\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\22222\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\22222\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\22222\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\22222\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\22222\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\22222\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\22222\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\22222\SFDSystem.deps.json
