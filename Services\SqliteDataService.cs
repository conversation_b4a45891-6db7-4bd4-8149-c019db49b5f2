using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// مزوّد بيانات SQLite يُنفّذ كل عمليات IDataService.
    /// أزلنا إضافة السائقين الافتراضيين فقط؛ جميع الدوال الأخرى بقيت كما كانت.
    /// </summary>
    public partial class SqliteDataService : IDataService, IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly ErrorHandlingService _errorHandler;
        // تم إزالة IItineraryService لتبسيط الحل

        public SqliteDataService()
        {
            _context = new ApplicationDbContext();
            _errorHandler = new ErrorHandlingService();
            // تم إزالة ItineraryService لتبسيط الحل
            _context.Database.EnsureCreated();

            // تحديث قاعدة البيانات لإضافة العمود الجديد
            UpdateDatabaseSchema();

            SeedInitialData();
            SeedProjectsData();
            System.Diagnostics.Debug.WriteLine("🗄️ SQLite DataService initialized successfully!");
        }

        #region Database Schema Updates
        /// <summary>
        /// تحديث مخطط قاعدة البيانات لإضافة الأعمدة الجديدة
        /// </summary>
        private void UpdateDatabaseSchema()
        {
            try
            {
                // التحقق من وجود عمود SelectedDrivers في جدول FieldVisits
                var connection = _context.Database.GetDbConnection();
                connection.Open();

                var command = connection.CreateCommand();
                command.CommandText = "PRAGMA table_info(FieldVisits)";

                var reader = command.ExecuteReader();
                bool hasSelectedDriversColumn = false;

                while (reader.Read())
                {
                    var columnName = reader.GetString(1); // Column name is at index 1
                    if (columnName == "SelectedDrivers")
                    {
                        hasSelectedDriversColumn = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة العمود إذا لم يكن موجود<|im_start|>
                if (!hasSelectedDriversColumn)
                {
                    var alterCommand = connection.CreateCommand();
                    alterCommand.CommandText = "ALTER TABLE FieldVisits ADD COLUMN SelectedDrivers TEXT";
                    alterCommand.ExecuteNonQuery();

                    System.Diagnostics.Debug.WriteLine("✅ Added SelectedDrivers column to FieldVisits table");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ SelectedDrivers column already exists in FieldVisits table");
                }

                // التحقق من وجود عمود VisitNotes في جدول FieldVisits
                reader.Close();
                command.CommandText = "PRAGMA table_info(FieldVisits)";
                reader = command.ExecuteReader();
                bool hasVisitNotesColumn = false;

                while (reader.Read())
                {
                    var columnName = reader.GetString(1);
                    if (columnName == "VisitNotes")
                    {
                        hasVisitNotesColumn = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة عمود VisitNotes إذا لم يكن موجود
                if (!hasVisitNotesColumn)
                {
                    var alterCommand = connection.CreateCommand();
                    alterCommand.CommandText = "ALTER TABLE FieldVisits ADD COLUMN VisitNotes TEXT DEFAULT ''";
                    alterCommand.ExecuteNonQuery();

                    System.Diagnostics.Debug.WriteLine("✅ Added VisitNotes column to FieldVisits table");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ VisitNotes column already exists in FieldVisits table");
                }

                // التحقق من وجود عمود SecurityRoute في جدول FieldVisits
                command.CommandText = "PRAGMA table_info(FieldVisits)";
                reader = command.ExecuteReader();
                bool hasSecurityRouteColumn = false;

                while (reader.Read())
                {
                    var columnName = reader.GetString(1);
                    if (columnName == "SecurityRoute")
                    {
                        hasSecurityRouteColumn = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة عمود SecurityRoute إذا لم يكن موجود
                if (!hasSecurityRouteColumn)
                {
                    var alterCommand = connection.CreateCommand();
                    alterCommand.CommandText = "ALTER TABLE FieldVisits ADD COLUMN SecurityRoute TEXT DEFAULT ''";
                    alterCommand.ExecuteNonQuery();

                    System.Diagnostics.Debug.WriteLine("✅ Added SecurityRoute column to FieldVisits table");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ SecurityRoute column already exists in FieldVisits table");
                }

                connection.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating database schema: {ex.Message}");
            }
        }
        #endregion

        #region Initial Data
        private void SeedInitialData()
        {
            try
            {
                if (!_context.Sectors.Any())
                {
                    var sectors = new List<Sector>
                    {
                        new Sector { Code = "QTAA", Name = "القطاع", Description = "القطاع العام" },
                        new Sector { Code = "training", Name = "التدريب", Description = "قطاع التدريب والتطوير" },
                        new Sector { Code = "contracts", Name = "التعاقدات", Description = "قطاع التعاقدات والمشتريات" },
                        new Sector { Code = "education", Name = "التعليم", Description = "قطاع التعليم والتربية" },
                        new Sector { Code = "empowerment", Name = "التمكين", Description = "قطاع التمكين والتطوير" },
                        new Sector { Code = "accounts", Name = "الحسابات", Description = "قطاع الحسابات والمالية" },
                        new Sector { Code = "agriculture", Name = "الزراعة", Description = "قطاع الزراعة والثروة الحيوانية" },
                        new Sector { Code = "complaints_compliance", Name = "الشكاوى والامتثال", Description = "قطاع الشكاوى والامتثال" },
                        new Sector { Code = "health_social_protection", Name = "الصحة والحماية الاجتماعية", Description = "قطاع الصحة والحماية الاجتماعية" },
                        new Sector { Code = "roads", Name = "الطرق", Description = "قطاع الطرق والمواصلات" },
                        new Sector { Code = "technical", Name = "الفنية", Description = "قطاع الشؤون الفنية" },
                        new Sector { Code = "monitoring_evaluation", Name = "المراقبة والتقييم", Description = "قطاع المراقبة والتقييم" },
                        new Sector { Code = "water_environment", Name = "المياه والبيئة", Description = "قطاع المياه والبيئة" },
                        new Sector { Code = "cash_for_work", Name = "النقد مقابل العمل", Description = "قطاع النقد مقابل العمل" }
                    };

                    _context.Sectors.AddRange(sectors);
                    _context.SaveChanges();
                    System.Diagnostics.Debug.WriteLine($"✅ Added {sectors.Count} initial sectors");
                }

                // إضافة موظفين أولية لكل قطاع
                if (!_context.Officers.Any())
                {
                    var officers = new List<Officer>();
                    var sectorIds = _context.Sectors.Select(s => s.Id).ToList();

                    foreach (var sectorId in sectorIds)
                    {
                        officers.Add(new Officer { Name = $"مدير القطاع {sectorId}", Rank = "مدير", Code = $"MGR{sectorId:D3}", SectorId = sectorId });
                        officers.Add(new Officer { Name = $"نائب مدير القطاع {sectorId}", Rank = "نائب مدير", Code = $"DEP{sectorId:D3}", SectorId = sectorId });
                        officers.Add(new Officer { Name = $"موظف أول القطاع {sectorId}", Rank = "موظف أول", Code = $"EMP{sectorId:D3}1", SectorId = sectorId });
                        officers.Add(new Officer { Name = $"موظف القطاع {sectorId}", Rank = "موظف", Code = $"EMP{sectorId:D3}2", SectorId = sectorId });
                    }

                    _context.Officers.AddRange(officers);
                    _context.SaveChanges();
                    System.Diagnostics.Debug.WriteLine($"✅ Added {officers.Count} initial officers");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error seeding initial data: {ex.Message}");
            }
        }

        private void SeedProjectsData()
        {
            try
            {
                if (!_context.Projects.Any())
                {
                    var projectsSeeder = new ProjectsSeeder(this);
                    var task = Task.Run(async () => await projectsSeeder.SeedProjectsAsync());
                    task.Wait();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشاريع الأولية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة بيانات المشاريع: {ex.Message}");
            }
        }
        #endregion

        #region Drivers
        public async Task<List<Driver>> GetDriversAsync() => await _context.Drivers.ToListAsync();

        public async Task<bool> AddDriverAsync(Driver driver)
        {
            if (string.IsNullOrEmpty(driver.DriverCode))
                driver.DriverCode = $"911{DateTime.Now:yyyyMMddHHmmss}";

            driver.CreatedAt = DateTime.Now;
            driver.IsActive = true;

            _context.Drivers.Add(driver);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateDriverAsync(Driver driver)
        {
            _context.Drivers.Update(driver);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteDriverAsync(int driverId)
        {
            var driver = await _context.Drivers.FindAsync(driverId);
            if (driver == null) return false;
            _context.Drivers.Remove(driver);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteVehicleAsync(int vehicleId)
        {
            var vehicle = await _context.Vehicles.FindAsync(vehicleId);
            if (vehicle == null) return false;
            _context.Vehicles.Remove(vehicle);
            return await _context.SaveChangesAsync() > 0;
        }
        #endregion

        #region Sectors
        public async Task<List<Sector>> GetSectorsAsync() => await _context.Sectors.ToListAsync();
        public async Task<bool> AddSectorAsync(Sector sector)
        {
            _context.Sectors.Add(sector);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<Sector?> GetSectorByCodeAsync(string sectorCode)
        {
            return await _context.Sectors.FirstOrDefaultAsync(s => s.Code == sectorCode);
        }
        #endregion

        #region Officers
        public async Task<List<Officer>> GetOfficersAsync() => await _context.Officers.ToListAsync();
        public async Task<List<Officer>> GetOfficersBySectorAsync(int sectorId) =>
            await _context.Officers.Where(o => o.SectorId == sectorId).ToListAsync();
        public async Task<bool> AddOfficerAsync(Officer officer)
        {
            _context.Officers.Add(officer);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<bool> UpdateOfficerAsync(Officer officer)
        {
            _context.Officers.Update(officer);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<bool> DeleteOfficerAsync(int officerId)
        {
            var officer = await _context.Officers.FindAsync(officerId);
            if (officer == null) return false;
            _context.Officers.Remove(officer);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<Officer?> GetOfficerByCodeAsync(string officerCode)
        {
            return await _context.Officers.FirstOrDefaultAsync(o => o.Code == officerCode);
        }
        #endregion

        #region Vehicles
        public async Task<List<Vehicle>> GetVehiclesAsync() => await _context.Vehicles.ToListAsync();
        public async Task<bool> AddVehicleAsync(Vehicle vehicle)
        {
            _context.Vehicles.Add(vehicle);
            return await _context.SaveChangesAsync() > 0;
        }
        #endregion

        #region Field Visits
        public async Task<List<FieldVisit>> GetFieldVisitsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 === بدء تحميل الزيارات الميدانية ===");

                // تحقق من عدد الزيارات في قاعدة البيانات
                var totalCount = await _context.FieldVisits.CountAsync();
                System.Diagnostics.Debug.WriteLine($"📊 إجمالي الزيارات في قاعدة البيانات: {totalCount}");

                var visits = await _context.FieldVisits
                    .Include(fv => fv.Visitors)
                    .Include(fv => fv.Projects)
                        .ThenInclude(p => p.Project)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم تحميل {visits.Count} زيارة من قاعدة البيانات");

                // تحميل خط السير والمشاريع لكل زيارة بالطريقة المحسنة
                foreach (var visit in visits)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 تحميل خط السير للزيارة {visit.Id} - {visit.VisitNumber}");

                    var itineraries = await _context.FieldVisitItineraries
                        .Where(i => i.FieldVisitId == visit.Id && i.IsActive)
                        .OrderBy(i => i.DayNumber)
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {itineraries.Count} أيام خط السير للزيارة {visit.VisitNumber}");

                    visit.Itinerary = itineraries.Select(i => i.ItineraryText).ToList();

                    for (int i = 0; i < visit.Itinerary.Count; i++)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 الزيارة {visit.VisitNumber} - اليوم {i + 1}: '{visit.Itinerary[i]}'");
                    }

                    // تشخيص المشاريع
                    System.Diagnostics.Debug.WriteLine($"🔍 المشاريع للزيارة {visit.VisitNumber}: {visit.Projects?.Count ?? 0} مشروع");
                    if (visit.Projects?.Any() == true)
                    {
                        for (int i = 0; i < visit.Projects.Count; i++)
                        {
                            var project = visit.Projects[i];
                            System.Diagnostics.Debug.WriteLine($"🔍 المشروع {i + 1}: رقم='{project.ProjectNumber}', اسم='{project.ProjectName}'");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد مشاريع للزيارة {visit.VisitNumber}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل جميع الزيارات مع خط السير بنجاح");
                return visits;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting field visits: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return new List<FieldVisit>();
            }
        }

        public async Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit visit)
        {
            var errors = new List<string>();

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء إضافة الزيارة: {visit.VisitNumber}");

                // تجميع جميع الأخطاء قبل الحفظ
                await ValidateFieldVisitAsync(visit, errors);

                // إذا كانت هناك أخطاء، إرجاعها دون حفظ
                if (errors.Any())
                {
                    await transaction.RollbackAsync();
                    return (false, errors);
                }

                // حفظ الزيارة أولاً
                _context.FieldVisits.Add(visit);
                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الزيارة الأساسية: {visit.VisitNumber} (ID: {visit.Id})");

                // حفظ خط السير بالطريقة المحسنة
                if (visit.Itinerary?.Any() == true)
                {
                    await SaveItineraryAsync(visit.Id, visit.Itinerary);
                    await _context.SaveChangesAsync(); // حفظ خط السير
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ خط السير: {visit.Itinerary.Count} أيام");
                }

                // حفظ المشاريع
                if (visit.Projects?.Any() == true)
                {
                    await SaveProjectsSimpleAsync(visit.Id, visit.Projects);
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ المشاريع: {visit.Projects.Count} مشروع");
                }

                await transaction.CommitAsync();
                System.Diagnostics.Debug.WriteLine($"🎯 تم إضافة الزيارة بنجاح: {visit.VisitNumber}");
                return (true, new List<string>());
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();

                // استخدام مركز التحكم الموحد للأخطاء
                var errorResult = _errorHandler.HandleException(ex,
                    context: "إضافة زيارة ميدانية",
                    userAction: $"إضافة زيارة رقم {visit.VisitNumber}");

                errors.Add(errorResult.UserMessage);
                return (false, errors);
            }
        }

        private async Task ValidateFieldVisitAsync(FieldVisit visit, List<string> errors)
        {
            // التحقق من البيانات الأساسية
            if (string.IsNullOrWhiteSpace(visit.VisitNumber))
            {
                errors.Add("رقم الزيارة مطلوب ولا يمكن أن يكون فارغاً");
            }

            if (string.IsNullOrWhiteSpace(visit.MissionPurpose))
            {
                errors.Add("مهمة النزول مطلوبة ولا يمكن أن تكون فارغة");
            }

            if (visit.SectorId <= 0)
            {
                errors.Add("يجب اختيار قطاع صحيح للزيارة");
            }

            // التحقق من عدم تكرار رقم الزيارة
            if (!string.IsNullOrWhiteSpace(visit.VisitNumber))
            {
                var existingVisit = await _context.FieldVisits.FirstOrDefaultAsync(v => v.VisitNumber == visit.VisitNumber);
                if (existingVisit != null)
                {
                    errors.Add($"رقم الزيارة '{visit.VisitNumber}' موجود مسبقاً في النظام");
                }
            }

            // التحقق من وجود القطاع
            if (visit.SectorId > 0)
            {
                var sector = await _context.Sectors.FindAsync(visit.SectorId);
                if (sector == null)
                {
                    errors.Add($"القطاع بالمعرف {visit.SectorId} غير موجود في النظام");
                }
            }

            // التحقق من وجود الضباط المحددين
            if (visit.Visitors?.Any() == true)
            {
                for (int i = 0; i < visit.Visitors.Count; i++)
                {
                    var visitor = visit.Visitors[i];
                    if (visitor.OfficerId > 0)
                    {
                        var officer = await _context.Officers.FindAsync(visitor.OfficerId);
                        if (officer == null)
                        {
                            errors.Add($"الضابط في الموضع {i + 1} (المعرف: {visitor.OfficerId}) غير موجود في النظام");
                        }
                    }
                }
            }

            // التحقق من صحة بيانات المشاريع
            if (visit.Projects?.Any() == true)
            {
                ValidateProjectsData(visit.Projects, errors);
            }
        }

        private void ValidateProjectsData(List<FieldVisitProject> projects, List<string> errors)
        {
            for (int i = 0; i < projects.Count; i++)
            {
                var project = projects[i];
                if (string.IsNullOrWhiteSpace(project.ProjectNumber))
                {
                    errors.Add($"رقم المشروع في الموضع {i + 1} فارغ أو غير صحيح");
                }
                if (string.IsNullOrWhiteSpace(project.ProjectName))
                {
                    errors.Add($"اسم المشروع في الموضع {i + 1} فارغ أو غير صحيح");
                }
            }

            // التحقق من عدم تكرار أرقام المشاريع
            var duplicateNumbers = projects.Where(p => !string.IsNullOrWhiteSpace(p.ProjectNumber))
                                          .GroupBy(p => p.ProjectNumber)
                                          .Where(g => g.Count() > 1)
                                          .Select(g => g.Key)
                                          .ToList();
            if (duplicateNumbers.Any())
            {
                errors.Add($"أرقام المشاريع التالية مكررة: {string.Join(", ", duplicateNumbers)}");
            }
        }



        public async Task<bool> UpdateFieldVisitAsync(FieldVisit visit)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء تحديث الزيارة: {visit.VisitNumber} (ID: {visit.Id})");

                // تحديث الزيارة
                _context.FieldVisits.Update(visit);

                // تحديث خط السير بالطريقة المحسنة
                await UpdateItineraryAsync(visit.Id, visit.Itinerary);

                // تحديث المشاريع
                if (visit.Projects?.Any() == true)
                {
                    await UpdateProjectsAsync(visit.Id, visit.Projects);
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث المشاريع: {visit.Projects.Count} مشروع");
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث الزيارة بنجاح: {visit.VisitNumber}");
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();

                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الزيارة: {ex.Message}");

                // استخدام مركز التحكم الموحد للأخطاء
                var errorResult = _errorHandler.HandleException(ex,
                    context: "تحديث زيارة ميدانية",
                    userAction: $"تحديث زيارة رقم {visit.VisitNumber}");

                return false;
            }
        }

        public async Task<bool> DeleteFieldVisitAsync(int id)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(id);
                if (visit == null) return false;

                _context.FieldVisits.Remove(visit);
                // خط السير سيتم حذفه تلقائياً بسبب Cascade Delete

                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                // استخدام مركز التحكم الموحد للأخطاء
                var errorResult = _errorHandler.HandleException(ex,
                    context: "حذف زيارة ميدانية",
                    userAction: $"حذف زيارة بالمعرف {id}");

                return false;
            }
        }

        /// <summary>
        /// حفظ خط السير في جدول منفصل - الحل الأصلي المبسط
        /// </summary>
        private Task SaveItineraryAsync(int fieldVisitId, List<string> itinerary)
        {
            if (itinerary == null || !itinerary.Any()) return Task.CompletedTask;

            for (int i = 0; i < itinerary.Count; i++)
            {
                if (!string.IsNullOrWhiteSpace(itinerary[i]))
                {
                    var itineraryDay = new FieldVisitItinerary
                    {
                        FieldVisitId = fieldVisitId,
                        DayNumber = i + 1,
                        ItineraryText = itinerary[i]
                    };
                    _context.FieldVisitItineraries.Add(itineraryDay);
                }
            }

            System.Diagnostics.Debug.WriteLine($"✅ Saved {itinerary.Count} itinerary days for visit {fieldVisitId}");
            return Task.CompletedTask;
        }

        /// <summary>
        /// تحديث خط السير - الحل الأصلي المبسط
        /// </summary>
        private async Task UpdateItineraryAsync(int fieldVisitId, List<string> itinerary)
        {
            // حذف خط السير القديم
            var oldItineraries = await _context.FieldVisitItineraries
                .Where(i => i.FieldVisitId == fieldVisitId)
                .ToListAsync();
            _context.FieldVisitItineraries.RemoveRange(oldItineraries);

            // حفظ خط السير الجديد
            await SaveItineraryAsync(fieldVisitId, itinerary);
        }
        public async Task<bool> ClearAllFieldVisitsAsync()
        {
            _context.FieldVisits.RemoveRange(_context.FieldVisits);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string contract) =>
            await _context.FieldVisits.Include(fv => fv.Visitors)
                                       .FirstOrDefaultAsync(fv => fv.DriverContract == contract);
        #endregion

        #region Projects
        public async Task<List<Project>> GetProjectsAsync() => await _context.Projects.ToListAsync();
        public async Task<Project?> GetProjectByNumberAsync(string number) =>
            await _context.Projects.FirstOrDefaultAsync(p => p.ProjectNumber == number);
        public async Task<bool> AddProjectAsync(Project project)
        {
            project.CreatedAt = DateTime.Now;
            _context.Projects.Add(project);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<bool> UpdateProjectAsync(Project project)
        {
            _context.Projects.Update(project);
            return await _context.SaveChangesAsync() > 0;
        }
        public async Task<bool> DeleteProjectAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project == null) return false;
            _context.Projects.Remove(project);
            return await _context.SaveChangesAsync() > 0;
        }
        #endregion

        #region Driver Quotes
        public async Task<List<DriverQuote>> GetDriverQuotesAsync()
        {
            try
            {
                return await _context.DriverQuotes.OrderByDescending(q => q.QuoteDate).ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting driver quotes: {ex.Message}");
                return new List<DriverQuote>();
            }
        }

        public async Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status)
        {
            try
            {
                return await _context.DriverQuotes
                    .Where(q => q.Status == status)
                    .OrderByDescending(q => q.QuoteDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting driver quotes by status: {ex.Message}");
                return new List<DriverQuote>();
            }
        }

        public async Task<bool> AddDriverQuoteAsync(DriverQuote quote)
        {
            try
            {
                quote.QuoteDate = DateTime.Now;
                _context.DriverQuotes.Add(quote);
                var result = await _context.SaveChangesAsync() > 0;

                if (result)
                {
                    // تحديث حالة السائق
                    var driver = await _context.Drivers.FindAsync(quote.DriverId);
                    if (driver != null)
                    {
                        driver.HasQuote = true;
                        driver.QuotedPrice = quote.QuotedPrice;
                        driver.QuotedDays = quote.QuotedDays;
                        driver.QuoteDate = quote.QuoteDate;
                        driver.QuoteNotes = quote.Notes;
                        await _context.SaveChangesAsync();
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ Driver quote added: {quote.DriverName} - {quote.FormattedPrice}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateDriverQuoteAsync(DriverQuote quote)
        {
            try
            {
                _context.DriverQuotes.Update(quote);
                var result = await _context.SaveChangesAsync() > 0;

                if (result)
                {
                    // تحديث حالة السائق
                    var driver = await _context.Drivers.FindAsync(quote.DriverId);
                    if (driver != null)
                    {
                        driver.QuotedPrice = quote.QuotedPrice;
                        driver.QuotedDays = quote.QuotedDays;
                        driver.QuoteNotes = quote.Notes;
                        await _context.SaveChangesAsync();
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ Driver quote updated: {quote.DriverName}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteDriverQuoteAsync(int quoteId)
        {
            try
            {
                var quote = await _context.DriverQuotes.FindAsync(quoteId);
                if (quote == null) return false;

                _context.DriverQuotes.Remove(quote);
                var result = await _context.SaveChangesAsync() > 0;

                if (result)
                {
                    // تحديث حالة السائق
                    var driver = await _context.Drivers.FindAsync(quote.DriverId);
                    if (driver != null)
                    {
                        driver.HasQuote = false;
                        driver.QuotedPrice = null;
                        driver.QuotedDays = null;
                        driver.QuoteDate = null;
                        driver.QuoteNotes = string.Empty;
                        await _context.SaveChangesAsync();
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ Driver quote deleted: {quote.DriverName}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error deleting driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status)
        {
            try
            {
                var quote = await _context.DriverQuotes.FindAsync(quoteId);
                if (quote == null) return false;

                quote.Status = status;
                _context.DriverQuotes.Update(quote);

                System.Diagnostics.Debug.WriteLine($"✅ Driver quote status updated: {quote.DriverName} - {status}");
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating driver quote status: {ex.Message}");
                return false;
            }
        }

        public async Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId)
        {
            try
            {
                return await _context.DriverQuotes
                    .FirstOrDefaultAsync(q => q.DriverId == driverId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting driver quote by driver ID: {ex.Message}");
                return null;
            }
        }
        #endregion

        #region Offers Methods

        /// <summary>
        /// حفظ نص الرسالة للسائق الفائز
        /// </summary>
        public async Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string messageText)
        {
            try
            {
                // البحث عن الزيارة الميدانية
                var fieldVisit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (fieldVisit != null)
                {
                    // حفظ نص الرسالة للسائق الفائز
                    fieldVisit.WinnerDriverMessage = messageText;

                    await _context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ نص الرسالة للسائق الفائز في الزيارة {visitNumber}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على الزيارة {visitNumber}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ نص الرسالة للسائق الفائز: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عروض الزيارة
        /// </summary>
        public async Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount)
        {
            try
            {
                // البحث عن الزيارة الميدانية
                var fieldVisit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (fieldVisit != null)
                {
                    // إضافة العروض إلى ملاحظات الزيارة
                    var offersNote = $"\n--- عروض الأسعار ({DateTime.Now:yyyy/MM/dd HH:mm}) ---\n{offersText}\n--- نهاية العروض ---\n";

                    // إضافة إلى الملاحظات الموجودة
                    fieldVisit.MissionPurpose += offersNote;

                    await _context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ عروض الزيارة {visitNumber}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على الزيارة {visitNumber}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ عروض الزيارة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على عروض الزيارة
        /// </summary>
        public async Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber)
        {
            try
            {
                var offers = new List<DriverOffer>();

                // البحث في جدول DriverQuotes للعروض المرتبطة بالزيارة
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Notes.Contains(visitNumber) || dq.Status == QuoteStatus.Accepted)
                    .ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    var offer = new DriverOffer
                    {
                        DriverId = quote.DriverId,
                        DriverName = quote.DriverName,
                        DriverCode = quote.DriverCode,
                        PhoneNumber = quote.PhoneNumber,
                        VehicleType = quote.VehicleType,
                        VehicleNumber = quote.VehicleNumber,
                        DaysCount = quote.QuotedDays,
                        ProposedAmount = quote.QuotedPrice,
                        IsSelected = quote.Status == QuoteStatus.Accepted,
                        IsWinner = false
                    };

                    offers.Add(offer);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم الحصول على {offers.Count} عرض للزيارة {visitNumber}");
                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على عروض الزيارة: {ex.Message}");
                return new List<DriverOffer>();
            }
        }

        /// <summary>
        /// حذف عروض الزيارة
        /// </summary>
        public async Task<bool> DeleteVisitOffersAsync(string visitNumber)
        {
            try
            {
                // حذف العروض من جدول DriverQuotes
                var quotesToDelete = await _context.DriverQuotes
                    .Where(dq => dq.Notes.Contains(visitNumber))
                    .ToListAsync();

                if (quotesToDelete.Any())
                {
                    _context.DriverQuotes.RemoveRange(quotesToDelete);
                    await _context.SaveChangesAsync();

                    System.Diagnostics.Debug.WriteLine($"✅ تم حذف {quotesToDelete.Count} عرض للزيارة {visitNumber}");
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف عروض الزيارة: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Field Visit Projects Methods

        /// <summary>
        /// حفظ المشاريع بطريقة مبسطة وآمنة
        /// </summary>
        private async Task SaveProjectsSimpleAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            try
            {
                if (projects?.Any() != true) return;

                foreach (var project in projects)
                {
                    var newProject = new FieldVisitProject
                    {
                        Id = 0,
                        FieldVisitId = fieldVisitId,
                        ProjectId = project.ProjectId,
                        ProjectNumber = project.ProjectNumber ?? "",
                        ProjectName = project.ProjectName ?? "",
                        DisplayOrder = project.DisplayOrder,
                        Notes = project.Notes ?? "",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    _context.FieldVisitProjects.Add(newProject);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إعداد {projects.Count} مشروع للحفظ");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في إعداد المشاريع: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث المشاريع بطريقة آمنة
        /// </summary>
        private async Task UpdateProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            try
            {
                // حذف المشاريع القديمة
                var existingProjects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                if (existingProjects.Any())
                {
                    _context.FieldVisitProjects.RemoveRange(existingProjects);
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف {existingProjects.Count} مشروع قديم");
                }

                // إضافة المشاريع الجديدة
                if (projects?.Any() == true)
                {
                    await SaveProjectsSimpleAsync(fieldVisitId, projects);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث المشاريع: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ مشاريع الزيارة الميدانية - نسخة آمنة ومحسنة
        /// </summary>
        public async Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            var errors = new List<string>();

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء حفظ مشاريع الزيارة {fieldVisitId}");

                // التحقق من وجود الزيارة أولاً
                var visit = await _context.FieldVisits.FindAsync(fieldVisitId);
                if (visit == null)
                {
                    errors.Add($"الزيارة بالمعرف {fieldVisitId} غير موجودة في النظام");
                    return (false, errors);
                }

                // التحقق من وجود مشاريع للحفظ
                if (projects?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ لا توجد مشاريع لحفظها للزيارة {fieldVisitId}");
                    return (true, errors);
                }

                // التحقق من صحة بيانات المشاريع
                ValidateProjectsData(projects, errors);
                if (errors.Any())
                {
                    return (false, errors);
                }

                // حذف المشاريع الموجودة للزيارة
                var existingProjects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                if (existingProjects.Any())
                {
                    _context.FieldVisitProjects.RemoveRange(existingProjects);
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف {existingProjects.Count} مشروع قديم للزيارة {fieldVisitId}");
                }

                // إضافة المشاريع الجديدة
                for (int i = 0; i < projects.Count; i++)
                {
                    var project = projects[i];

                    // إنشاء مشروع جديد لتجنب مشاكل Entity Framework
                    var newProject = new FieldVisitProject
                    {
                        Id = 0, // كائن جديد
                        FieldVisitId = fieldVisitId,
                        ProjectId = project.ProjectId,
                        ProjectNumber = project.ProjectNumber ?? "",
                        ProjectName = project.ProjectName ?? "",
                        DisplayOrder = i + 1,
                        Notes = project.Notes ?? "",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    _context.FieldVisitProjects.Add(newProject);

                    System.Diagnostics.Debug.WriteLine($"➕ إضافة مشروع {i + 1}: رقم='{newProject.ProjectNumber}', " +
                        $"اسم='{newProject.ProjectName}', ProjectId={newProject.ProjectId}");
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {projects.Count} مشروع جديد للزيارة {fieldVisitId}");

                return (true, errors);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ مشاريع الزيارة: {ex.Message}");

                // استخدام مركز التحكم الموحد للأخطاء
                var errorResult = _errorHandler.HandleException(ex,
                    context: "حفظ مشاريع الزيارة الميدانية",
                    userAction: $"حفظ مشاريع للزيارة {fieldVisitId}");

                errors.Add(errorResult.UserMessage);
                return (false, errors);
            }
        }

        /// <summary>
        /// جلب مشاريع الزيارة الميدانية
        /// </summary>
        public async Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId)
        {
            try
            {
                var projects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId && p.IsActive)
                    .Include(p => p.Project)
                    .OrderBy(p => p.DisplayOrder)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم جلب {projects.Count} مشروع للزيارة {fieldVisitId}");
                return projects;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب مشاريع الزيارة: {ex.Message}");
                return new List<FieldVisitProject>();
            }
        }

        /// <summary>
        /// جلب مشاريع الزيارة بمعرف الزيارة
        /// </summary>
        public async Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId)
        {
            return await GetFieldVisitProjectsAsync(visitId);
        }

        /// <summary>
        /// دالة طوارئ لاستعادة البيانات التجريبية
        /// </summary>
        public async Task<bool> RestoreSampleDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚨 بدء استعادة البيانات التجريبية...");

                // إضافة زيارات تجريبية
                var sampleVisits = new List<FieldVisit>
                {
                    new FieldVisit
                    {
                        VisitNumber = "001",
                        DriverContract = "2025001",
                        AddDate = DateTime.Now.AddDays(-5),
                        DepartureDate = DateTime.Now.AddDays(-3),
                        ReturnDate = DateTime.Now.AddDays(-1),
                        DaysCount = 3,
                        MissionPurpose = "زيارة استطلاعية للمشاريع",
                        SectorId = 1,
                        SectorName = "قطاع التنمية",
                        VisitorsCount = 2,
                        ProjectsCount = 2
                    },
                    new FieldVisit
                    {
                        VisitNumber = "002",
                        DriverContract = "2025002",
                        AddDate = DateTime.Now.AddDays(-3),
                        DepartureDate = DateTime.Now.AddDays(-1),
                        ReturnDate = DateTime.Now.AddDays(1),
                        DaysCount = 3,
                        MissionPurpose = "متابعة تنفيذ المشاريع",
                        SectorId = 1,
                        SectorName = "قطاع التنمية",
                        VisitorsCount = 3,
                        ProjectsCount = 1
                    }
                };

                foreach (var visit in sampleVisits)
                {
                    _context.FieldVisits.Add(visit);
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم استعادة {sampleVisits.Count} زيارة تجريبية");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استعادة البيانات: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Report Data Methods

        /// <summary>
        /// جلب عروض الأسعار للزيارة المحددة مع ربطها بجدول السائقين لجلب رقم التلفون
        /// </summary>
        public async Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(visitId);
                if (visit == null) return new List<PriceOfferItem>();

                var offers = await GetVisitOffersAsync(visit.VisitNumber);
                var priceOffers = new List<PriceOfferItem>();

                for (int i = 0; i < offers.Count; i++)
                {
                    var offer = offers[i];

                    // البحث عن السائق في جدول السائقين الرئيسي لجلب رقم التلفون
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.Name == offer.DriverName ||
                                                 d.DriverCode == offer.DriverCode ||
                                                 d.Id == offer.DriverId);

                    // استخدام رقم التلفون من جدول السائقين إذا وُجد، وإلا استخدم المحفوظ في العرض
                    var phoneNumber = driver?.PhoneNumber ?? offer.PhoneNumber ?? "غير محدد";

                    priceOffers.Add(new PriceOfferItem
                    {
                        SerialNumber = i + 1,
                        DriverName = offer.DriverName,
                        PhoneNumber = phoneNumber,
                        OfferedPrice = offer.ProposedAmount,
                        Status = offer.IsSelected ? "مقبول" : "مرفوض"
                    });
                }

                return priceOffers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting price offers: {ex.Message}");
                return new List<PriceOfferItem>();
            }
        }

        /// <summary>
        /// جلب بيانات السيارة المختارة للزيارة
        /// </summary>
        public async Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(visitId);
                if (visit == null) return null;

                var offers = await GetVisitOffersAsync(visit.VisitNumber);
                var selectedOffer = offers.FirstOrDefault(o => o.IsSelected);

                if (selectedOffer != null)
                {
                    return new SelectedVehicleData
                    {
                        DriverName = selectedOffer.DriverName,
                        PhoneNumber = selectedOffer.PhoneNumber,
                        VehicleType = selectedOffer.VehicleType,
                        VehicleModel = "2020", // Default value
                        VehicleColor = "أبيض", // Default value
                        PlateNumber = selectedOffer.VehicleNumber
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting selected vehicle: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Misc
        public async Task RefreshAllDataAsync() => await _context.Database.EnsureCreatedAsync();
        public void Dispose() => _context.Dispose();
        #endregion
    }
}
